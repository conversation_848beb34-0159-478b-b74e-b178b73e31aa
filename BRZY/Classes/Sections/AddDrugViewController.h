//
//  AddDrugViewController.h
//  BRZY
//  添加药材页面
//  Created by e<PERSON><PERSON> on 2017/10/16.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BaseViewController.h"

#import "SocketManager.h"

#import "BRPrescriptionModel.h"
#import "BRPatientModel.h"

#import "BRFactoryModel.h"
#import "BRSubFactoryModel.h"

#import "BRPrescriptionInputPanel.h"

@protocol AddDrugVCDelegate<NSObject>

/**
 保存药材

 @param drugList 要保存的药材
 @param balanceWeight 重量
 @param drugPrice 药材的价格
 @param makeCost 制作费用
 @param makeDays 制作天数
 @param makeDesc 制作的描述
 @param weight    总重
 */
- (void)addDrugListToPrescriptionWithDrugList:(NSArray *)drugList detailSelectedIndex:(NSInteger)detailSelectedIndex masterSelectedIndex:(NSInteger)masterSelectedIndex balanceWeight:(NSString *)balanceWeight drugPrice:(NSString *)drugPrice makeCost:(NSString *)makeCost makeDays:(NSString *)makeDays makeDesc:(NSString *)makeDesc weight:(CGFloat)weight;
- (void)popViewControllerClearDrug;

/**
 添加药材工具条清空已经选择的药材
 */
- (void)cleanSelectedDrugs;

// 显示特殊煎法帮助信息
- (void)showUseTypeHelpInfo;

@end

@interface AddDrugViewController : BaseViewController <SocketManagerDelegate>

@property (nonatomic, strong) NSString *patientId;//患者的id

@property (nonatomic, strong) NSString *titleStr;//添加药材页面的title

//快速开方 是否怀孕
@property (nonatomic, copy) NSString *takerIsPregnant;


@property (nonatomic, strong) NSMutableArray *drugArray;//修改药材，已经保存过的药材数组
- (void)setDrugArray:(NSMutableArray *)drugArray;

@property (nonatomic, strong) NSMutableArray *factoryArray;//厂家与剂型信息
@property (nonatomic, assign) NSInteger masterSelectedIndex;//选择剂型 颗粒、饮片、膏方等左边一列的index
@property (nonatomic, assign) NSInteger detailSelectedIndex;//选择剂型 比如颗粒下面的华润三九、康仁堂等右边一列的index
@property (nonatomic, strong) BRFactoryModel *factoryModel;//被选中的厂家
@property (nonatomic, strong) BRSubFactoryModel *subFactoryModel;//被选中的剂型

/**
 设置剂型与厂家信息

 @param factoryArr 厂家与剂型信息
 @param masterSelectedIndex 选择剂型 颗粒、饮片、膏方等左边一列的index
 @param detailSelectedIndex 选择剂型 比如颗粒下面的华润三九、康仁堂等右边一列的index
 */
- (void)setTitleWithFactoryArray:(NSArray *)factoryArr masterSelectedIndex:(NSInteger)masterSelectedIndex detailSelectedIndex:(NSInteger)detailSelectedIndex;

@property (nonatomic, strong) BRPatientModel *patientModel;//患者的信息

@property (nonatomic, strong) id<AddDrugVCDelegate> delegate;



@property (nonatomic, strong) BRPrescriptionInputPanel *inputView;//药材搜索的view

@property (nonatomic, assign) BOOL isTempPres;//是不是临时药方

@property (nonatomic, assign) BRPrescribeType prescribeType;

@end
