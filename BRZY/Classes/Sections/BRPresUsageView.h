//
//  BRPresUsageView.h
//  BRZY
//  开处方用法用量
//  Created by eas<PERSON> on 2017/10/13.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "BRUnderlineRedTextField.h"

typedef void(^ChangeTimeBlock)(void);
typedef void(^DrugNumChanged)(NSString *);
typedef void(^textFieldEditingDidBegin)(void);
typedef void(^changeDayBlock)(NSString *);
typedef void(^changeUsageHeight)(CGFloat);
typedef void(^ClickOtherTypeMarkBlock)(void);
// 添加新的block类型用于处理代煎剂型下"每剂分几次服用"的点击事件
typedef void(^ServingTimesSelectionBlock)(void);

typedef NS_ENUM(NSInteger, BRDrugType){
    BRDrugTypeKAndY,//颗粒饮片外用中药
    BRDrugTypeOther//其他的膏方一类的
};

@interface BRPresUsageView : UIView

@property (nonatomic, copy) ChangeTimeBlock changeTime;
@property (nonatomic, strong) UILabel *totalDoseLabel;
@property (nonatomic, strong) BRUnderlineRedTextField *drugNumTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *usageTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *timesTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *preDoseTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *dayTextField;
@property (nonatomic, strong) UIButton *changeTimeButton;
@property (nonatomic, copy) DrugNumChanged drugChanged;
@property (nonatomic, copy) textFieldEditingDidBegin textFieldEditingDidBegin;
//胶囊克数输入
@property (nonatomic, copy) textFieldEditingDidBegin capsuleInputEditingDidbegin;

// 添加新的属性用于处理代煎剂型下"每剂分几次服用"的点击事件
@property (nonatomic, copy) ServingTimesSelectionBlock servingTimesSelectionBlock;

//胶囊颗粒数显示  只有胶囊的时候需要显示
@property (nonatomic, strong) UILabel *capsuleParticleLabel;

//胶囊时候设置颗数数量
@property (nonatomic, copy) NSString *capsuleParticleNum;


@property (nonatomic, copy) changeDayBlock changeDay;
@property (nonatomic, copy) ClickOtherTypeMarkBlock clickOtherTypeBlock;
@property (nonatomic, copy) changeUsageHeight changeUsageHeight;

//蜜丸规格
@property (nonatomic, strong) UIButton *changeHoneyPillSpecificationButton;

@property (nonatomic, strong) NSString *drugForm;
- (void)setDrugForm:(NSString *)drugForm;
@property (nonatomic, assign) BRDrugType drugType;

@property (nonatomic, strong) NSString *totalDoseStr;
- (void)setTotalDoseStr:(NSString *)totalDoseStr;

@property (nonatomic, copy) NSString *patientId;
//快速开方时间戳 临时id
@property (nonatomic, copy) NSString *quickOrderTempId;


// 新增用药方法选择按钮
@property (nonatomic, strong) UIButton *internalUseButton;  // 内服按钮
@property (nonatomic, strong) UIButton *externalUseButton;  // 外用按钮
@property (nonatomic, strong) UILabel *usageMethodLabel;    // "用药方法"标签
@property (nonatomic, copy) NSString *selectedMode;         // 选中的用药方法

// 新增规格行相关属性
@property (nonatomic, strong) UILabel *specificationLabel;  // "规格:"标签
@property (nonatomic, assign) BOOL isSpecificationRowVisible; // 规格行是否可见

// 规格行控制方法
- (void)setSpecificationRowVisible:(BOOL)visible;
- (BOOL)isSpecificationRowVisible;

@end
