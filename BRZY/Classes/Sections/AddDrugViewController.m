//
//  AddDrugViewController.m
//  BRZY
//  添加药材页面
//  Created by <PERSON><PERSON><PERSON> on 2017/10/16.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "AddDrugViewController.h"
#import "BRComPrescriptionViewController.h"
#import "FactoryInfoViewController.h"

#import "BRPresNoticeView.h"
#import "BRPresDrugCell.h"
#import "BRPresHaveNoticeCell.h"
#import "BRActionSheetView.h"
#import "UINavigationBar+Addition.h"
#import "BRPresSelectTypeView.h"
#import "BRPresInfoView.h"
#import "BRMedicationWarning.h"
#import "BRAlertView.h"
#import "BRPrescriptionToolBar.h"
#import "BRGuidePageView.h"

#import "BRSubMedicineModel.h"
#import "BRRiskTipModel.h"
#import "BRCustomPopupView.h"

#import "FloatingButton.h"
#import "BRAdjustByMultipleContentView.h"
#import "BRIntelligentEntryViewController.h"
#import "CustomBoilyWayViewController.h"


#define TEXT_FIELD_END_EDIT      @"textFieldEndEdit"
#define TEXT_FIELD_SHOULD_RETURN @"textFieldShoudReturn"

#define kNavHeight        (isiPhoneX ? 91.f : 71.f)


typedef NS_ENUM(NSInteger, EditMode) {
    EditModeNormal,
    EditModeForward,
    EditModeBackward,
    EditModeReplace
};

typedef void(^UpdateBoilyWayBlock)(NSMutableArray *totalUseTypeArr, NSString *errorMsg);


@interface AddDrugViewController ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout,BRPrescriptionInputPanelDelegate,BRComPrescriptionVCDelegagte, BRIntelligentDelegate>

@property (nonatomic, strong) BRPresNoticeView *presNoticeView;//提示添加特殊煎法的view
@property (nonatomic, strong) NSTimer *timer;//提示剂量部分的计时
@property (nonatomic, strong) UICollectionView *collectionView;//用来显示已添加药材的view
@property (nonatomic, strong) BRActionSheetView *useTypeActionSheet;//选择特殊用法
@property (nonatomic, strong) BRActionSheetView *chooseEditModeActionSheet; //选择编辑模式
@property (nonatomic, strong) BRPresSelectTypeView *selectTypeView;//选择厂商弹出框
@property (nonatomic, strong) BRPresInfoView *presInfoView;//显示药方的详情的view
@property (nonatomic, strong) BRPrescriptionToolBar *presToolBar;//添加药材工具条

//@property (nonatomic, strong) NSMutableArray *factoryArr;//药厂信息

@property (nonatomic, strong) NSMutableArray *useTypeArr;//保存特殊煎法
@property (nonatomic, strong) NSArray *baseUseTypeArr;//非自定义的特殊煎法
@property (nonatomic, strong) NSMutableArray *pasteDrugArr;//粘贴的药材

@property (nonatomic, strong) NSString *navBarTitleStr;//自定义导航栏上的标题
@property (nonatomic, strong) UIView *customTitleView;//自定义的title view

//@property (nonatomic, strong) FloatingButton *floatingButton; //设置弹出显示的浮动弹窗

//当前是否为单列显示
@property (nonatomic, assign) bool isSingleShow;


@property (nonatomic, copy) NSString *showNoticeTimeString;

//当前编辑模式
@property (nonatomic, assign) EditMode mEditmode;
//当前编辑模式对应的位置索引
@property (nonatomic, assign) NSInteger mEditIndex;


@property (nonatomic, strong) UIButton *topTipsButton;

@property (nonatomic, copy) NSString *drugForm;


@end

@implementation AddDrugViewController

- (void)setTitleWithFactoryArray:(NSArray *)factoryArr masterSelectedIndex:(NSInteger)masterSelectedIndex detailSelectedIndex:(NSInteger)detailSelectedIndex {
    
    _factoryArray = [[NSMutableArray alloc] initWithArray:factoryArr];
    
    _masterSelectedIndex = masterSelectedIndex;
    _detailSelectedIndex = detailSelectedIndex;
    
    _factoryModel = [_factoryArray objectAtIndex:masterSelectedIndex];
    NSArray *detailArr = _factoryModel.list;
    _subFactoryModel = [detailArr objectAtIndex:detailSelectedIndex];
    
    _navBarTitleStr = [NSString stringWithFormat:@"%@(%@)",_factoryModel.drugFormName,_subFactoryModel.factoryName];
    
    
//    NSLog(@"设置bar title == %@",_factoryModel.drugFormName);
    
    
}

- (void)setTitleViewWithTitleStr:(NSString *)titleStr{
    
    //获取drugForm
    NSString *drugForm = [[titleStr componentsSeparatedByString:@"("] firstObject];
    
    NSLog(@"set bar title == %@",drugForm);
    
    _customTitleView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kNavHeight)];
    _customTitleView.backgroundColor = [UIColor clearColor];
    
    NSMutableAttributedString *titleString = [[NSMutableAttributedString alloc]initWithString:titleStr];
    
    NSRange range = [titleStr rangeOfString:@"("];
    
    CGSize titleStrSize = [ViewTools sizeLabelToFit:titleString width:MAXFLOAT height:20];
    
    [titleString addAttribute:NSForegroundColorAttributeName value:[UIColor br_textBlackColor] range:NSMakeRange(0, range.location)];
    [titleString addAttribute:NSFontAttributeName value:kFontRegular(19) range:NSMakeRange(0, range.location)];
    [titleString addAttribute:NSForegroundColorAttributeName value:[UIColor br_textGrayColor] range:NSMakeRange(range.location, titleStr.length-range.location)];
    [titleString addAttribute:NSFontAttributeName value:kFontRegular(15) range:NSMakeRange(range.location, titleStr.length-range.location)];
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake((_customTitleView.width-titleStrSize.width)/2, (isiPhoneX ? 43.f : 23.f), titleStrSize.width, 20)];
    titleLabel.attributedText = titleString;
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.backgroundColor = [UIColor clearColor];
    [_customTitleView addSubview:titleLabel];
    
    NSString *titleText = @"更换供货商";
    UIFont *titleFont = kFontRegular(14);
    
    UIButton *titleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    titleButton.titleLabel.font = titleFont;
    [titleButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
    [titleButton setTitle:titleText forState:UIControlStateNormal];
    [titleButton setImage:[UIImage imageNamed:@"prescription_change_time"] forState:UIControlStateNormal];
    [titleButton addTarget:self action:@selector(pressToChangeMode) forControlEvents:UIControlEventTouchUpInside];
    [_customTitleView addSubview:titleButton];
    
    CGFloat titleWidth = [Utils widthForString:titleText withFont:titleFont] + 30;
    
    
    titleButton.titleEdgeInsets = UIEdgeInsetsMake(0, -15, 0, 15);
    titleButton.imageEdgeInsets = UIEdgeInsetsMake(0, 75, 0, -75);
//    titleButton.frame = CGRectMake((kScreenWidth - (kScreenWidth-100*2))/2, kNavHeight-31, kScreenWidth-100*2, 30);
    titleButton.frame = CGRectMake( (kScreenWidth - titleWidth) / 2.0, kNavHeight - 31, titleWidth, 30);
    titleButton.backgroundColor = [UIColor clearColor];
    
    UIImage *tipsImage = [UIImage imageNamed:@"yangshengpu_jiangli"];
    UIButton *topTipsButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [topTipsButton setImage:tipsImage forState:UIControlStateNormal];
    [_customTitleView addSubview:topTipsButton];
    [topTipsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(tipsImage.size);
        make.left.equalTo(titleButton.mas_right).with.offset(15);
        make.centerY.equalTo(titleButton);
    }];
    self.topTipsButton = topTipsButton;
    
    [topTipsButton addTarget:self action:@selector(clickTopTipsButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    
//    self.topTipsButton.hidden = YES;
    
    UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [backButton setImage:[UIImage imageNamed:@"navi_back_btn"] forState:UIControlStateNormal];
    backButton.frame = CGRectMake(0, (isiPhoneX ? 50.f : 30.f), 50, 30);
    [backButton addTarget:self action:@selector(clickBackButton:) forControlEvents:UIControlEventTouchUpInside];
    [_customTitleView addSubview:backButton];
    
    UIButton *saveButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [saveButton setTitle:@"保存" forState:UIControlStateNormal];
    [saveButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
    saveButton.titleLabel.font = kFontRegular(15);
    saveButton.frame = CGRectMake(kScreenWidth-65, (isiPhoneX ? 50.f : 30.f), 50, 30);
    [saveButton addTarget:self action:@selector(clickSaveDrugButtonEvent) forControlEvents:UIControlEventTouchUpInside];
    [_customTitleView addSubview:saveButton];
    
    UIImageView *lineImage = [[UIImageView alloc] initWithFrame:CGRectMake(0, kNavHeight-1, kScreenWidth, 0.7)];
    lineImage.backgroundColor = [UIColor br_divisionLineColor];
    [_customTitleView addSubview:lineImage];
    
    [self.view addSubview:_customTitleView];
    
    [self changeInfoIconVisibilityWithDrugForm:drugForm];
    
}

- (void)viewWillAppear:(BOOL)animated {
    
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBar.hidden = YES;
    
}

- (void)popViewController {
    
    if (_inputView) {
        _inputView.inputDelegate = nil;
    }
    
    [[SocketManager shareInstance]removeDelegate:self];
    
    if (_drugArray.count == 0 && _isTempPres) {
        [self.delegate popViewControllerClearDrug];
    }
    
    [self.navigationController popViewControllerAnimated:YES];
    
}

- (void)setDrugArray:(NSMutableArray *)drugArray {
    
    if (drugArray.count == 0) {
        return;
    }

    //进行深拷贝，切断数组的上下文
    _drugArray = [NSMutableArray new];
    for (NSInteger index=0; index<drugArray.count; index++) {
        
        BRSubMedicineModel *model = [drugArray[index] copy];
        [_drugArray addObject:model];
        
    }
    
    //获取已经添加药材的药品信息
    if (_isTempPres) {
        [self requestDrugsInfoWithDrugArr:_drugArray messageId:@"replaceDrugs"];
    }
    
}
#pragma mark - click event
//点击保存药品
- (void)clickSaveDrugButtonEvent {
    
    [self.view endEditing:YES];
    
    if (_drugArray.count == 0) {
        [self.view makeToast:@"请添加药材" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    NSMutableArray *toastArr = [[NSMutableArray alloc]initWithCapacity:0];
    for (BRSubMedicineModel *model in _drugArray) {
        
        if ([model.stock isEqualToString:@"0"]) {
            [toastArr addObject:model];
            NSString *toastStr = [NSString stringWithFormat:@"\"%@\"库存暂缺",model.drugName];
            [self.view makeToast:toastStr duration:1 position:CSToastPositionCenter];
            break;
        }
        if ([model.dose isEqualToString:@"0"] || [model.dose floatValue] == 0) {
            [toastArr addObject:model];
            NSString *toastStr = [NSString stringWithFormat:@"请输入\"%@\"剂量",model.drugName];
            [self.view makeToast:toastStr duration:1 position:CSToastPositionCenter];
            break;
        }
        
    }
    if (toastArr.count > 0) {
        return;
    }
    
    NSMutableArray *preDetailList = [[NSMutableArray alloc]init];
    for (NSInteger index=0; index<_drugArray.count; index++) {
        
        BRSubMedicineModel *model = [_drugArray objectAtIndex:index];
        
        NSString *useMethod = @"";
        if (model.useMethod) {
            useMethod = model.useMethod;
        }
        
        NSString *description = @"";
        
        NSDictionary *specialUnit = model.specialUnit;
        
        if (!model.specialUnit) {
            specialUnit = @{};
        }
        
        NSDictionary *dict = @{
                               @"dose":model.dose,
                               @"unit":model.unit,
                               @"useMethod":useMethod,
                               @"drugName":model.drugName,
                               @"description":description,
                               @"index":[NSString stringWithFormat:@"%ld",index],
                               @"drugId":model.drugId,
                               @"specialUnit":specialUnit
                                   };
        
        [preDetailList addObject:dict];
        
    }
    
    //如果是否怀孕是nil，那么默认为怀孕
    if (!_patientModel.isPregnant) {
        _patientModel.isPregnant = @"0";
    }
    
    //药材的规则验证，风险提示
    
    NSMutableDictionary *order = [NSMutableDictionary dictionaryWithDictionary:@{
        @"preName":@"",
        @"drugForm":_factoryModel.drugFormName,
        @"drugType":_subFactoryModel.productSubType,
//        @"isPregnant":_patientModel.isPregnant,
//        @"takerId":_patientModel.patientId,
        @"preDetailList":preDetailList
    }];
    
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [order setValuesForKeysWithDictionary:@{
            @"isPregnant":_patientModel.isPregnant,
            @"takerId":_patientModel.patientId,
        }];
    }
    //如果是快速开方
    else {
        if (self.takerIsPregnant.length > 0) {
            [order setValue:self.takerIsPregnant forKey:@"isPregnant"];
        }
    }
    
    //青庐药局判断
    if ([_subFactoryModel.factoryName containsString:@"青庐药局"] &&
        ([self.drugForm isEqualToString:@"饮片"] || [self.drugForm isEqualToString:@"代煎"])) {
        NSLog(@"青庐药局====");
        
        if (_drugArray.count < 2) {
            BRAlertView *alertView = [[BRAlertView alloc] init];
            __weak BRAlertView *weakAlertView = alertView;
            alertView.isHideWhenTapBackground = YES;
            [alertView showAlertView:@"基于此药房规范，单一药味无法调剂，请选择其他厂商或继续添加药味" completion:^{
                [weakAlertView close];
            }];
            
            return;
        }
    }
    
    NSString *orderStr = [order mj_JSONString];
    
    NSDictionary *dict = @{
                           @"method_code":@"000272",
                           @"order":orderStr
                           };
    
    __weak AddDrugViewController *weakSelf = self;
    MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@""];
    __weak MBProgressHUD *weakHud = hud;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        weakHud.hidden = YES;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            //这里提示给医生
            NSArray *validataRuleList = [BRRiskTipModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"validataRuleList"]];
            
            NSLog(@"validate rule list = %@",validataRuleList);
            
            if (validataRuleList.count > 0) {
                
                
                NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
                paragraphStyle.paragraphSpacing = 5;
                paragraphStyle.lineSpacing = 2;
                paragraphStyle.headIndent = 25.0f;
                
                NSDictionary *attributes = @{
                                             NSFontAttributeName : kFontLight(16),
                                             NSForegroundColorAttributeName : [UIColor colorWithRGB:0x1d2024],
                                             NSParagraphStyleAttributeName : paragraphStyle
                                             };
                

                NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:@""];
                
                for (NSInteger index=0; index<validataRuleList.count; index++) {
                    
                    BRRiskTipModel *riskTipModel = [validataRuleList objectAtIndex:index];
                    NSString *riskTipStr = [NSString stringWithFormat:@"%ld、%@%@",index+1,riskTipModel.titleText,(index == validataRuleList.count - 1) ? @"" : @"\n"];
                    
                    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:riskTipStr attributes:attributes];
                    
                    if (riskTipModel.drugName) {
                        
                        NSArray *drugArray = [riskTipModel.drugName componentsSeparatedByString:@","];
                        
                        for (NSString *name in drugArray) {
                            
                            NSArray *rangesArray = [self rangesOfString:name inString:riskTipStr];
                            
                            for (int j = 0; j < rangesArray.count; j++) {
                                NSValue *value = [rangesArray objectAtIndex:j];
                                
                                NSRange range = [value rangeValue];
                                
                                if (range.location != NSNotFound) {
                                    [string setAttribute:NSForegroundColorAttributeName value:[UIColor br_textRedColor] range:range];
                                }
                            }
                        }
                    }
                    
                    [attributedString appendAttributedString:string];
                }
                
                [weakSelf.view endEditing:YES];
                
                [Utils br_showMedicationWarningAlertViewWithMessage:attributedString completion:^(BOOL isNext) {
                    if (isNext) {
                        [self requestCharge];
                    }
                }];
            }else{
                [self requestCharge];
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        weakHud.hidden = YES;
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

- (NSArray *)rangesOfString:(NSString *)searchString inString:(NSString *)str {
    NSMutableArray *results = [NSMutableArray array];
    NSRange searchRange = NSMakeRange(0, [str length]);
    NSRange range;
    while ((range = [str rangeOfString:searchString options:0 range:searchRange]).location != NSNotFound) {
        [results addObject:[NSValue valueWithRange:range]];
        searchRange = NSMakeRange(NSMaxRange(range), [str length] - NSMaxRange(range));
    }
    return results;
}

- (void)clickBackButton:(UIButton *)sender {
    
    if (_drugArray.count > 0) {
        
        __weak AddDrugViewController *weakSelf = self;
        [self.view endEditing:YES];
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
//        __weak BRAlertView *weakAlertView = alertView;
        alertView.isHideWhenTapBackground = YES;
        [alertView showAlertViewWithCancelButton:@"已添加药材，是否保存？" completion:^(BOOL isOk) {
            
            if (isOk) {
                [weakSelf clickSaveDrugButtonEvent];
            }
            else {
                [weakSelf.drugArray removeAllObjects];
                [weakSelf popViewController];
            }
            
        }];
        
    }
    else {
        [self popViewController];
    }
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    CGFloat scale = [Config getFontSizeScale];
    UICollectionViewFlowLayout *flowLayout = [[UICollectionViewFlowLayout alloc] init];
    flowLayout.itemSize = CGSizeMake(kScreenWidth/2, 60 * scale); // 根据缩放比例调整cell高度
    
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:flowLayout];

    //初始化各种数据
    if (!_drugArray) {
        _drugArray = [[NSMutableArray alloc]initWithCapacity:0];
    }
    _useTypeArr = [[NSMutableArray alloc]initWithCapacity:0];
    _pasteDrugArr = [[NSMutableArray alloc]initWithCapacity:0];
    
    _showNoticeTimeString = @"";
    //设置默认编辑模式为正常
    _mEditmode = EditModeNormal;
    //对应的位置
    _mEditIndex = -1;
    
    [self setTitleViewWithTitleStr:_navBarTitleStr];
    
    //创建UI部分
    [self configUI];
    
    if (_drugArray.count > 0) {
        [self collectionViewReloadData];
    }
    
    [[SocketManager shareInstance]addDelegate:self];
    
    //获取之前保存的，是否为单列显示
    self.isSingleShow = [[NSUserDefaults standardUserDefaults] boolForKey:@"show_isSingleShow"];
    
//    if (self.isSingleShow) {
//        NSLog(@"单列显示===");
//    }else{
//        NSLog(@"双列显示===");
//    }
    
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
    self.navigationController.navigationBar.hidden = NO;
    
}


- (void)clickTopTipsButtonEvent:(UIButton *)sender {
//    NSLog(@"点击 Toptips button");
    
    
    [self.view endEditing:YES];
    
    
    NSString *text = @"";
    
    
    if ([self.drugForm isEqualToString:@"颗粒"]) {
        text = @"按照传统正常饮片一天剂量开方后，点击【按倍改量】按钮，可快速将传统配方颗粒剂量切换为国省标剂量【0.5倍，0.6倍，0.7倍，0.8倍，默认0.6倍】。";
    }else if ([self.drugForm isEqualToString:@"饮片"] || [self.drugForm isEqualToString:@"代煎"]){
        text = @"1、点击药名可设置特殊煎法；长按药名可编辑药材顺序、选择替换药。\n2、药名后( )内容为药味别名，【】内容为药味规格。";
    }else if ([self.drugForm isEqualToString:@"膏方"] || [self.drugForm isEqualToString:@"水丸"] || [self.drugForm isEqualToString:@"蜜丸"] || [self.drugForm isEqualToString:@"散剂"]
              || [self.drugForm isEqualToString:@"胶囊"]){
        text = @"按照一天剂量开方后，点击【按倍改量】按钮，可快速修改剂量【7倍，14倍，30倍，60倍】，剂量修改为【7天量，14天量,30天量，60天量】。";
    }
    
    NSString *factoryName = self.subFactoryModel.factoryName;
    
    if ([self.drugForm isEqualToString:@"颗粒"] && [factoryName isEqualToString:@"必然甄选【平台定制】"]) {
        text = @"该颗粒为合煎颗粒(药味浓缩干燥)，非国标配方颗粒，不需要减量开方，按照正常饮片处方即可。";
    }
    
    if ([text isEqualToString:@""]) {
        return;
    }
    
    [Utils br_showTipsInfoWithTitle:@"提示信息" message:text btnTitle:@"知道了" handleBlock:^{
        
    }];
}


#pragma mark - config UI
- (void)configUI {
    
    __weak AddDrugViewController *weakSelf = self;
    
    self.view.backgroundColor = [UIColor br_backgroundColor];
    
    _presNoticeView = [[BRPresNoticeView alloc]init];
    _presNoticeView.noticeStr = @"点击药名选择特殊煎药方式";
    _presNoticeView.hidden = YES;
    [self.view addSubview:_presNoticeView];
    
    [_presNoticeView mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.top.equalTo(self.view.mas_top).offset(kNavHeight);
        make.left.and.right.equalTo(weakSelf.view);
        make.height.mas_equalTo(0);
        
    }];
    
    _presNoticeView.closeButtonBlock = ^ {
        [weakSelf hiddenNoticeView];
        
        if (weakSelf.showNoticeTimeString.length > 0 && [weakSelf.factoryModel.drugFormName isEqualToString:@"颗粒"]) {
            //进行时间保存
            [[NSUserDefaults standardUserDefaults] setObject:weakSelf.showNoticeTimeString forKey:@"pres_show_granule_notice_date"];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
    };
#pragma mark 输入框
    
    _inputView = [[BRPrescriptionInputPanel alloc]init];
    _inputView.inputDelegate = self;
    [self.view addSubview:_inputView];
    
    //设置引导页
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    BOOL isFirstShow = ![userDefault boolForKey:@"isFirstLoadAddDrugVC"];
    if (isFirstShow) {
        
        BRGuidePageView *guideView = [[BRGuidePageView alloc]init];
        if (kScreenWidth == 640) {
            guideView.bgImage = [UIImage imageNamed:@"add_drug_guide_5"];
        }
        else if (kScreenWidth == 750) {
            guideView.bgImage = [UIImage imageNamed:@"add_drug_guide_6"];
        }
        else {
            guideView.bgImage = [UIImage imageNamed:@"add_drug_guide_6p"];
        }
        
        guideView.closeButtonBlock = ^{
          
            [self displayNoticeView];
            [_inputView.inputField becomeFirstResponder];
        };
        
        [userDefault setBool:YES forKey:@"isFirstLoadAddDrugVC"];
        
    }
    else {
        
        [self displayNoticeView];
        [self showGranuleNoticeView];
        [_inputView.inputField becomeFirstResponder];
        
    }
    
    
    NSLayoutConstraint* bottomConstraint = [NSLayoutConstraint constraintWithItem:_inputView attribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeBottom multiplier:1.0f constant:(isiPhoneX?-kTabbarSafeBottomMargin:0)];
    _inputView.bottomConstraint = bottomConstraint;
    
    [_inputView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.left.and.right.equalTo(weakSelf.view);
        make.height.mas_equalTo(42);
        
    }];
    [self.view addConstraint:bottomConstraint];
    
    __weak BRPrescriptionInputPanel *weakInputView = _inputView;
    
    //搜索出来的药品展示更多
    _inputView.moreBlock = ^ (BOOL isShowing){
        
        if (isShowing) {
            [weakSelf.presToolBar setToolBarStateHidden];
        }
        else {
            [weakSelf.presToolBar setToolBarStateNormal];
        }
        
    };
    
    _inputView.drugTextFieldBlock = ^(NSString *drugStr) {
        
        NSString *newDrugStr = [drugStr stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if (newDrugStr.length != 0) {
            // 如果输入只有一个字符并且是英文字符，则清空数据，不进行检索
            if (newDrugStr.length == 1 && [[NSPredicate predicateWithFormat:@"SELF MATCHES %@", @"[a-zA-Z]"] evaluateWithObject:newDrugStr]) {
                [weakInputView setSearchResultArr:@[]];  // 清空数据
                return;
            }
            
            // 如果输入的第一个字符是中文，或者输入长度超过1个英文字符，则进行检索
            if (newDrugStr.length > 1 || [[NSPredicate predicateWithFormat:@"SELF MATCHES %@", @"[\\u4e00-\\u9fa5]"] evaluateWithObject:[newDrugStr substringToIndex:1]]) {
                NSDictionary *dict = @{
                                       @"code":@"0031",
                                       @"id":[Utils createMessageId],
                                       @"params":@{
                                               @"userid":[UserManager shareInstance].getUserId,
                                               @"keyword":newDrugStr,
                                               @"drugType":weakSelf.subFactoryModel.productSubType,
                                               @"drugForm":weakSelf.factoryModel.drugFormName,
                                               @"drugProviderId":weakSelf.subFactoryModel.factoryId,
                                               @"code":@"0031",
                                               @"sysType":@"2",
                                               @"appVer":[UIApplication sharedApplication].appVersion
                                               }
                                       };
                
                [[SocketManager shareInstance] sendDataDict:dict];
            }
        } else {
            [weakInputView setSearchResultArr:@[]];  // 输入为空时清空数据
        }
        
    };
    
    //跳转进入药方模板页面
    _inputView.pressToGotoModel = ^{
        
        [weakSelf changeToNormalEditMode];
        [weakSelf.collectionView reloadData];
      
        BRComPrescriptionViewController *comPrescriptionVC = [[BRComPrescriptionViewController alloc]init];
        comPrescriptionVC.patientId = weakSelf.patientId;
        comPrescriptionVC.drugType = weakSelf.subFactoryModel.productSubType;
        comPrescriptionVC.drugForm = weakSelf.factoryModel.drugFormName;
        comPrescriptionVC.selfSupport = weakSelf.subFactoryModel.selfSupport;
        comPrescriptionVC.delegate = weakSelf;
        comPrescriptionVC.drugProviderId = weakSelf.subFactoryModel.factoryId;
        comPrescriptionVC.addDrugViewController = weakSelf;
        [weakSelf.navigationController pushViewController:comPrescriptionVC animated:NO];
        
    };
    
    //智能录入
    _inputView.clickIntelligentEntryBlock = ^{
        NSLog(@"点击了智能录入=====");
        
        [weakSelf changeToNormalEditMode];
        [weakSelf.collectionView reloadData];
        
        BRIntelligentEntryViewController *intelligentEntryVC = [[BRIntelligentEntryViewController alloc] init];
        intelligentEntryVC.delegate = weakSelf;
        [weakSelf.navigationController pushViewController:intelligentEntryVC animated:YES];
    };
#pragma mark 工具栏
    _presToolBar = [[BRPrescriptionToolBar alloc]init];
    [self.view addSubview:_presToolBar];
    
    _presToolBar.drugNumStr = @"/共0味";
    
    if ([Config isCopyPrescription]) {
//        _presToolBar.pasteButton.selected = YES;
        _presToolBar.pasteButtonSelected = YES;
        _presToolBar.pasteButtonColor = [UIColor br_textBlueColor];
    }
    else {
//        _presToolBar.pasteButton.selected = NO;
        _presToolBar.pasteButtonSelected = NO;
        _presToolBar.pasteButtonColor = [UIColor br_textMediumGrayColor];
    }
    
    //粘贴用药
    _presToolBar.pasteButtonBlock = ^{

        //首次点击 提示
        if ([Config getisFirstShowCopyToast]) {
            
            [Config changeFirstShowCopyToNo];
            
            [weakSelf.view makeToast:@"患者医案中历史药方可复制到此" duration:kToastDuration position:CSToastPositionCenter];
            
            
            if (![Config isCopyPrescription]) {
                return;
            }
            
        }
        //非首次点击
        else {
            //没有复制内容
            if (![Config isCopyPrescription]) {
                return;
            }
            
        }
        
#pragma mark - 粘贴内容 --
        //粘贴复制内容操作
        NSArray *pasteDrugArr = [Config pastePrescription];
        
        _pasteDrugArr = [NSMutableArray arrayWithArray:pasteDrugArr];
        
        [weakSelf requestDrugsInfoWithDrugArr:weakSelf.pasteDrugArr messageId:@"pasteDrugs"];
        
    };
    
    [_presToolBar mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.bottom.equalTo(weakSelf.inputView.mas_top);
        make.height.mas_equalTo(70);
        make.left.and.right.equalTo(weakSelf.view);
        
    }];
    
    //清除药材的方法
    _presToolBar.clearButtonBlock = ^{
        
        if (_drugArray.count == 0) {
            return ;
        }
        
        [weakSelf.view endEditing:YES];
      
        BRAlertView *alertView = [[BRAlertView alloc] init];
        __weak BRAlertView *weakAlertView = alertView;
        alertView.isHideWhenTapBackground = YES;
        [alertView showAlertViewWithCancelButton:@"是否删除已添加的药材列表？" completion:^(BOOL isOk) {
            
            if (!isOk) {
                return ;
            }
            else {
                
                [weakSelf.drugArray removeAllObjects];
                
                [weakSelf collectionViewReloadData];
                
                //切换为正常模式
                [weakSelf changeToNormalEditMode];
                
                //清空药价
                weakSelf.presToolBar.priceStr = @"¥ 0.00";
                
                if ([weakSelf.delegate respondsToSelector:@selector(cleanSelectedDrugs)]) {
                    [weakSelf.delegate cleanSelectedDrugs];
                }
                
            }
            
        }];
        
    };
    
    //显示设置
    _presToolBar.showSetButtonBlock = ^{
        [weakSelf clickSquareEditButtonEvent:nil];
    };
    
    //按倍改量
    _presToolBar.adjustByMultipleButtonBlock = ^{
        
        //如果药材为空，则直接给出提示
        if (_drugArray.count == 0) {
            [weakSelf.view makeToast:@"请添加药材" duration:2 position:CSToastPositionCenter];
            return;
        }
        
        
        //按倍改量弹窗
        NSLog(@"按倍改量");
        BRAdjustByMultipleContentView *contentView = [[BRAdjustByMultipleContentView alloc] initWithFrame:CGRectMake(0, 0, 100, 230)];
        BRAlertView *customAlertView = [[BRAlertView alloc] init];
        
        [customAlertView showAlertViewWithCustomView:contentView title:@"按倍改量" subtitle:@"所有药材按倍改量后将四舍五入取整" completion:^(BOOL isOk) {
            if (isOk) {
                NSString *text = [[contentView.multipleTextField text] stringByTrim];
                if ([text hasSuffix:@"."]) {
                    //如果是，则去掉最后一个字符
                    text = [text substringToIndex:text.length - 1];
                }
//                text = [text stringByReplacingOccurrencesOfString:@"." withString:@""];
                if ([text isEqualToString:@""] || [text isEqualToString:@"0"]) {
                    [customAlertView makeToast:@"最小输入倍数为0.1" duration:1 position:CSToastPositionCenter];
                    return;
                }
                
                //获取到输入倍数
                NSDecimalNumber *factorDecimalNumber = [NSDecimalNumber decimalNumberWithString:text];
                [weakSelf changeDoseByMultipleWithFactor:factorDecimalNumber];
                
                [customAlertView close];
            }else{
                [customAlertView close];
            }
        }];
        
        [customAlertView.okButton setTitle:@"修改" forState:UIControlStateNormal];
    };
    
    
#pragma mark 展示药材区域
    
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    [layout setScrollDirection:UICollectionViewScrollDirectionVertical];
    
    _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0, kNavHeight, kScreenWidth, kScreenHeight-kNavHeight)  collectionViewLayout:layout];
    _collectionView.dataSource = self;
    _collectionView.delegate = self;
    _collectionView.backgroundColor = [UIColor br_backgroundColor];
    _collectionView.allowsMultipleSelection = YES;
    [self.view addSubview:_collectionView];
    
    [_collectionView registerClass:[BRPresDrugCell class] forCellWithReuseIdentifier:@"BRPresDrugCell"];
    [_collectionView registerClass:[BRPresHaveNoticeCell class] forCellWithReuseIdentifier:@"BRPresHaveNoticeCell"];

    
    [_collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
       
        make.top.equalTo(weakSelf.presNoticeView.mas_bottom);
        make.left.and.right.equalTo(weakSelf.view);
        make.bottom.equalTo(weakSelf.presToolBar.mas_top);
        
    }];
    
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleLongPress:)];
    [self.collectionView addGestureRecognizer:longPress];
    
    //浮动按钮
//    UIImage *btnImage = [[UIImage imageNamed:@"square.and.pencil"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
//    self.floatingButton = [FloatingButton buttonWithType:UIButtonTypeCustom];
//    self.floatingButton.frame = CGRectMake(0, 0, 50, 50);
//    self.floatingButton.backgroundColor = [UIColor br_textBlueColor];
//    self.floatingButton.layer.cornerRadius = 25;
////    [self.floatingButton setTitle:@"Drag" forState:UIControlStateNormal];
//    [self.floatingButton setImage:btnImage forState:UIControlStateNormal];
//    [self.floatingButton setImage:btnImage forState:UIControlStateHighlighted];
//    [self.floatingButton setImage:btnImage forState:UIControlStateFocused];
//    [self.view addSubview:self.floatingButton];
//    
//    [self.floatingButton addTarget:self action:@selector(clickSquareEditButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
//    
//    
//    self.floatingButton.center = CGPointMake(kScreenWidth - 25 - 20, kScreenHeight / 2 + 25);
    
    
    
    // 使用Masonry设置初始位置
//    [self.floatingButton mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.size.mas_equalTo(CGSizeMake(40, 40));
//        make.right.equalTo(self.view).offset(-10);
////        make.bottom.equalTo(self.presToolBar.mas_top).offset(-20);
//        make.centerY.equalTo(self.view).with.offset(-100);
//    }];
    
}

#pragma mark - BRIntelligentDelegate
- (void)BRIntelligentDelegateHerbInfoList:(NSArray<BRHerbInfo *> *)herbsInfoArray {
    if (!herbsInfoArray || herbsInfoArray.count == 0) {
        return;
    }
    
    // 新增: 创建一个可变数组用于保持顺序
    NSMutableArray *orderedArray = [NSMutableArray array];
    
    // 保持原有的 Dictionary 用于去重
    NSMutableDictionary *herbDict = [NSMutableDictionary dictionary];
    
    // 修改: 反向遍历数组,这样可以保证相同 drugName 的情况下,保留最后出现的元素
    // 同时使用 orderedArray 来维护顺序
    for (BRHerbInfo *herb in [herbsInfoArray reverseObjectEnumerator]) {
        if (herb.drugName && !herbDict[herb.drugName]) {
            // 将不重复的元素添加到 Dictionary 中用于去重
            herbDict[herb.drugName] = herb;
            // 将元素插入到数组最前面,这样可以保持原有顺序
            [orderedArray insertObject:herb atIndex:0];
        }
    }
    
    NSArray *uniqueArray = [orderedArray copy];
    
    
    //药材添加中
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"添加药材中..."];
    
    //获取药材
    NSMutableArray *herbNameArray = [NSMutableArray array];
    for (int i = 0; i < uniqueArray.count; i++) {
        BRHerbInfo *herbInfo = [uniqueArray objectAtIndex:i];
        [herbNameArray addObject:herbInfo.drugName];
    }
    
    NSString *herbArrayString = [herbNameArray mj_JSONString];
    
    //调用000451接口进行药材添加
    
    //drugType drugForm  drugProviderId(厂商编号)  nameList药品名称
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:@{
        @"method_code" : @"000451",
        @"appVer" : [UIApplication sharedApplication].appVersion,
        @"drugType":_subFactoryModel.productSubType,
        @"drugForm":_factoryModel.drugFormName,
        @"drugProviderId":_subFactoryModel.factoryId,
        @"nameList" : herbArrayString,
        @"dcId":@""
    }];
    
    __weak typeof(self) weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        [progressHUD hideAnimated:NO];
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSArray<BRSearchModel *> *array = [BRSearchModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"data"]];
            if (array.count > 0) {
                //获取药材的 id
                NSMutableArray *drugIdsArr = [NSMutableArray arrayWithCapacity:0];
                NSMutableArray *prescriptionArray = [NSMutableArray arrayWithCapacity:0];
                NSMutableArray *tempArray = [NSMutableArray arrayWithCapacity:0];

                // 先将 array 中的所有药品添加到临时数组
                for (BRSearchModel *model in array) {
                    BRSubMedicineModel *subModel = [[BRSubMedicineModel alloc] init];
                    subModel.drugName = model.name;
                    subModel.unit = model.unit;
                    subModel.useMethod = @"";
                    subModel.drugId = model.drugId;
                    subModel.dose = 0; // 默认剂量为0
                    
                    // 在 herbsArray 中查找匹配的药品
                    for (BRHerbInfo *herbInfo in uniqueArray) {
                        if ([herbInfo.drugName isEqualToString:model.name]) {
                            subModel.dose = herbInfo.dose;
                            //饮片和代煎的时候设置特殊煎法
                            if ([weakSelf.factoryModel.drugFormName isEqualToString:@"饮片"] || [weakSelf.factoryModel.drugFormName isEqualToString:@"代煎"]) {
                                subModel.useMethod = herbInfo.method;
                            }
                            
                            break;
                        }
                    }
                    
                    [tempArray addObject:subModel];
                }

                // 按照 herbsArray 的顺序重新排序
                for (BRHerbInfo *herbInfo in uniqueArray) {
                    for (BRSubMedicineModel *subModel in tempArray) {
                        if ([herbInfo.drugName isEqualToString:subModel.drugName]) {
                            [drugIdsArr addObject:subModel.drugId];
                            [prescriptionArray addObject:subModel];
                            break;
                        }
                    }
                }

                // 添加在 herbsArray 中没有的药品
                for (BRSubMedicineModel *subModel in tempArray) {
                    BOOL exists = NO;
                    for (BRSubMedicineModel *addedModel in prescriptionArray) {
                        if ([addedModel.drugName isEqualToString:subModel.drugName]) {
                            exists = YES;
                            break;
                        }
                    }
                    
                    if (!exists) {
                        [drugIdsArr addObject:subModel.drugId];
                        [prescriptionArray addObject:subModel];
                    }
                }
                
                weakSelf.pasteDrugArr = [NSMutableArray arrayWithArray:prescriptionArray];
                
                NSString *drugIdsString = [drugIdsArr componentsJoinedByString:@","];
                [weakSelf requestDrugsInfoWithDrugIdString:drugIdsString messageId:@"pasteDrugs"];
            }else{
                [weakSelf.view makeToast:@"未识别到有效药材" duration:2 position:CSToastPositionCenter];
            }
            NSLog(@"responseObject = %@",responseObject);
        }else{
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [progressHUD hideAnimated:NO];
        NSLog(@"error === %@",error.localizedDescription);
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
    }];
}




#pragma mark BRPrescriptionInputPanelDelegate

- (void)inputTextFieldDidBeginEditing {
    [self collectionViewScrollToBottom];
}
#pragma mark - 修改UI界面弹出
- (void)clickSquareEditButtonEvent:(UIButton *)sender {
    
    [self.view endEditing:YES];
    
    __weak typeof(self) weakSelf = self;
    __block BRCustomPopupView *popupView = [[BRCustomPopupView alloc] init];
    __weak BRCustomPopupView *weakPopupView = popupView;
    popupView.singleColumn = self.isSingleShow;
    
    
    popupView.layoutChangeCallback = ^(BOOL isSingleColumn) {
        //根据选择来更改选项
        weakSelf.isSingleShow = isSingleColumn;
        //更新UserDeafult中记录
        [[NSUserDefaults standardUserDefaults] setBool:isSingleColumn forKey:@"show_isSingleShow"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        
        //刷新当前显示界面
        //todo ....
        
        [weakSelf.collectionView reloadData];
    };
    
    //0-正常   1-大   2-超大
    popupView.fontSizeChangeCallback = ^(NSInteger fontSize) {
        // 保存字体大小设置
        [Config setFontSize:fontSize];
        
        // 1. 先获取所有 indexPaths
        NSMutableArray *allIndexPaths = [NSMutableArray array];
        NSInteger sections = [self.collectionView numberOfSections];
        for (NSInteger section = 0; section < sections; section++) {
            NSInteger rows = [self.collectionView numberOfItemsInSection:section];
            for (NSInteger row = 0; row < rows; row++) {
                [allIndexPaths addObject:[NSIndexPath indexPathForRow:row inSection:section]];
            }
        }
        
        // 2. 使用 performBatchUpdates 进行更新
        [self.collectionView performBatchUpdates:^{
            // 更新所有 cell 的字体大小,包括被遮挡的
            for (NSIndexPath *indexPath in allIndexPaths) {
                UICollectionViewCell *cell = [self.collectionView cellForItemAtIndexPath:indexPath];
                if ([cell isKindOfClass:[BRPresHaveNoticeCell class]]) {
                    [(BRPresHaveNoticeCell *)cell updateFontSize];
                } else if ([cell isKindOfClass:[BRPresDrugCell class]]) {
                    [(BRPresDrugCell *)cell updateFontSize];
                }
            }
            
            //如果是超大字体 并且是双行  ----> 改为默认单行
            self.isSingleShow = [[NSUserDefaults standardUserDefaults] boolForKey:@"show_isSingleShow"];
            if (fontSize == 2 && !self.isSingleShow) {
                [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"show_isSingleShow"];
                [[NSUserDefaults standardUserDefaults] synchronize];
                weakSelf.isSingleShow = YES;
                
                //改变弹窗界面中的选项单双排选项
                [weakPopupView changeSingleOrDoubleRowButtonShow:0];
            }
            
        } completion:^(BOOL finished) {
            // 3. 完成后重新加载数据并刷新布局
            [self.collectionView reloadData];
            [self.collectionView.collectionViewLayout invalidateLayout];
        }];
    };

    [popupView show];
}

#pragma mark - 根据drugForm设置顶部弹窗提示
- (void)changeInfoIconVisibilityWithDrugForm:(NSString *)drugForm {
    
    self.drugForm = drugForm;
    
    if ([drugForm isEqualToString:@"颗粒"] || [drugForm isEqualToString:@"饮片"] || [drugForm isEqualToString:@"代煎"] || [drugForm isEqualToString:@"膏方"] ||
        [drugForm isEqualToString:@"水丸"] || [drugForm isEqualToString:@"蜜丸"] || [drugForm isEqualToString:@"散剂"] || [drugForm isEqualToString:@"胶囊"]) {
        self.topTipsButton.hidden = NO;
    }else {
        self.topTipsButton.hidden = YES;
    }
}


#pragma mark 添加药材
- (void)addDrugWithSearchModel:(BRSearchModel *)searchModel {
    
    if ([searchModel.type isEqualToString:@"1"]) {
        
        //这里请求药品的详细数据
        [self requestDrugsInfoWithDrugIdString:searchModel.drugId messageId:@"insertSingleDrug"];
        
    }
    else {
        
        [self.inputView hiddenMoreDrugView];
        
        //这里用来添加处方
        NSDictionary *dict = @{
                               @"code":@"0032",
                               @"id":[NSString stringWithFormat:@"%@,%@",@"unlongPress",searchModel.name],
                               @"params":@{
                                       @"userid":[UserManager shareInstance].getUserId,
                                       @"preId":searchModel.drugId,
                                       @"preType":searchModel.type,
                                       @"drugType":_subFactoryModel.productSubType,
                                       @"drugForm":_factoryModel.drugFormName,
                                       @"drugProviderId":_subFactoryModel.factoryId,
                                       @"code":@"0032",
                                       @"sysType":@"2",
                                       @"appVer":[UIApplication sharedApplication].appVersion
                                       }
                               };
        
        [[SocketManager shareInstance] sendDataDict:dict];
        
    }
    
}

#pragma mark 长按 显示搜索出来的处方详情
- (void)showPrescriptionInfoWithSearchModel:(BRSearchModel *)searchModel {
    
    [self.inputView hiddenMoreDrugView];
    
    //这里用来添加处方
    NSDictionary *dict = @{
                           @"code":@"0032",
                           @"id":[NSString stringWithFormat:@"%@,%@",@"longPress",searchModel.name],
                           @"params":@{
                                   @"userid":[UserManager shareInstance].getUserId,
                                   @"preId":searchModel.drugId,
                                   @"preType":searchModel.type,
                                   @"drugType":_subFactoryModel.productSubType,
                                   @"drugForm":_factoryModel.drugFormName,
                                   @"drugProviderId":_subFactoryModel.factoryId,
                                   @"code":@"0032",
                                   @"sysType":@"2",
                                   @"appVer":[UIApplication sharedApplication].appVersion
                                   }
                           };
    
    [[SocketManager shareInstance] sendDataDict:dict];
    
}

#pragma mark 删除药材
- (void)deleteDrugWithDrugIndex:(NSInteger)index {
    // 参数检查
    if (index >= self.drugArray.count) {
        return;
    }
    
    [self.view endEditing:YES];
    
    // 获取要删除的药材模型
    BRSubMedicineModel *medicineModel = [self.drugArray objectAtIndex:index];
    
    // 添加: 构造带药材名的提示语
    NSString *alertMessage = [NSString stringWithFormat:@"是否删除药材【%@】？", medicineModel.drugName];
    
    __weak typeof(self) weakSelf = self;
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:@"删除" forState:UIControlStateNormal];
    alertView.isHideWhenTapBackground = YES;
    
    [alertView showAlertViewWithCancelButton:alertMessage completion:^(BOOL isOk) {
        if (!isOk) {
            return;
        }
        
        // 删除数据源中的药材
        [weakSelf.drugArray removeObjectAtIndex:index];
        // 刷新列表
        [weakSelf collectionViewReloadData];
    }];
}
#pragma mark 添加剂量
- (void)editSelectedDrugDose:(NSString *)doseStr indexPath:(NSIndexPath *)indexPath {
    
    [self.inputView showDrugView];
    
    BRSubMedicineModel *model = [_drugArray objectAtIndex:indexPath.row];
    if ([model.stock isEqualToString:@"0"]) {
        BRPresHaveNoticeCell *cell = (BRPresHaveNoticeCell *)[_collectionView cellForItemAtIndexPath:indexPath];
        cell.doseTextField.text = doseStr;
    }
    else {
        BRPresDrugCell *cell = (BRPresDrugCell *)[_collectionView cellForItemAtIndexPath:indexPath];
        cell.doseTextField.text = doseStr;
    }
    
    //计算药价
    [self calculateMultipleDrugPrice];
    
    model.dose = doseStr;
    [_drugArray replaceObjectAtIndex:indexPath.row withObject:model];
    
    [self.inputView.inputField becomeFirstResponder];
    self.inputView.inputField.text = @"";
    
    //获取联想
    [self requestAssociateDataWithDrugId:model.drugId];
    
}

#pragma mark 获取联想
- (void)requestAssociateDataWithDrugId:(NSString *)drugId {
    
    NSMutableArray *drugIdArr = [[NSMutableArray alloc]init];
    for (BRSubMedicineModel *medicineModel in _drugArray) {
        [drugIdArr addObject:medicineModel.drugId];
    }
    
    NSString *drugIds = [drugIdArr componentsJoinedByString:@","];
    
    NSDictionary *dict = @{
                           @"code":@"0034",
                           @"id":[Utils createMessageId],
                           @"params":@{
                                   @"userid":[UserManager shareInstance].getUserId,
                                   @"drugId":drugIds,
                                   @"drugType":_subFactoryModel.productSubType,
                                   @"drugForm":_factoryModel.drugFormName,
                                   @"drugProviderId":_subFactoryModel.factoryId,
                                   @"code":@"0034",
                                   @"sysType":@"2",
                                   @"appVer":[UIApplication sharedApplication].appVersion
                                   }
                           };
    
    [[SocketManager shareInstance] sendDataDict:dict];
    
}

#pragma mark -  输入框键盘
- (void)inputTextFieldDidEndEditing {
    
    [self.view endEditing:YES];
    
}

#pragma mark ---- UICollectionViewDataSource

// 添加: 通过drugId查找药材索引的方法
- (NSInteger)findIndexForDrugId:(NSString *)drugId {
    for (NSInteger i = 0; i < self.drugArray.count; i++) {
        BRSubMedicineModel *model = self.drugArray[i];
        if ([model.drugId isEqualToString:drugId]) {
            return i;
        }
    }
    return NSNotFound;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
    return 1;
}


- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return _drugArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    if (_drugArray.count == 0) {
        return nil;
    }
    
    BRSubMedicineModel *medicineModel = _drugArray[indexPath.row];
    
    if ([medicineModel.stock isEqualToString:@"0"] || medicineModel.useMethod.length != 0) {
        BRPresHaveNoticeCell *haveNoticeCell = [self configureNoticeCell:collectionView forIndexPath:indexPath withModel:medicineModel isSingleColumn:self.isSingleShow];
        [haveNoticeCell updateFontSize];
        return haveNoticeCell;
    } else {
        BRPresDrugCell *drugCell = [self configureDrugCell:collectionView forIndexPath:indexPath withModel:medicineModel isSingleColumn:self.isSingleShow];
        [drugCell updateFontSize];
        return drugCell;
    }
}

- (BRPresHaveNoticeCell *)configureNoticeCell:(UICollectionView *)collectionView forIndexPath:(NSIndexPath *)indexPath withModel:(BRSubMedicineModel *)medicineModel isSingleColumn:(BOOL)isSingle
{
    BRPresHaveNoticeCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"BRPresHaveNoticeCell" forIndexPath:indexPath];
    
    cell.isSingleShow = isSingle;
    
    [cell setMedicineModel:medicineModel];
    
    __weak typeof(self) weakSelf = self;
    
    // 修改: 使用drugId来定位药材
    NSString *drugId = medicineModel.drugId;
    
    cell.deleteDrugBlock = ^{
        [weakSelf changeToNormalEditMode];
        // 通过drugId查找当前索引
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        
        if(currentIndex != NSNotFound) {
            [weakSelf deleteDrugWithDrugIndex:currentIndex];
        }
    };
    
    [self configureDoseEditing:cell forModel:medicineModel atIndexPath:indexPath];
    
    cell.selectUseMethod = ^{
//        [weakSelf requestUseTypeForDrugWithId:medicineModel.drugId atIndexPath:indexPath];
        [weakSelf requestUseTypeWithDrugId:drugId];
    };
    
    //是否为编辑模式，样式调整
    if (self.mEditmode == EditModeNormal) {
        //正常模式
        cell.isEditSelected = NO;
    }else{
        //编辑模式
        if (indexPath.row == self.mEditIndex) {
            cell.isEditSelected = YES;
        }else{
            cell.isEditSelected = NO;
        }
    }
    
    return cell;
}

- (BRPresDrugCell *)configureDrugCell:(UICollectionView *)collectionView forIndexPath:(NSIndexPath *)indexPath withModel:(BRSubMedicineModel *)medicineModel isSingleColumn:(BOOL)isSingle
{
    BRPresDrugCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"BRPresDrugCell" forIndexPath:indexPath];
    [cell setMedicineModel:medicineModel];
    
    cell.isSingleShow = isSingle;
    
    __weak typeof(self) weakSelf = self;
    // 修改: 使用drugId来定位药材
    NSString *drugId = medicineModel.drugId;
    
    cell.deleteDrugBlock = ^{
        [weakSelf changeToNormalEditMode];
        // 通过drugId查找当前索引
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        if(currentIndex != NSNotFound) {
           [weakSelf deleteDrugWithDrugIndex:currentIndex];
        }
    };
    
    [self configureDoseEditing:cell forModel:medicineModel atIndexPath:indexPath];
    
    cell.selectUseMethod = ^{
//        [weakSelf requestUseTypeForDrugWithId:medicineModel.drugId atIndexPath:indexPath];
        [weakSelf requestUseTypeWithDrugId:drugId];
    };
    
    //是否为编辑模式，样式调整
    if (self.mEditmode == EditModeNormal) {
        //正常模式
        cell.isEditSelected = NO;
    }else{
        //编辑模式
        if (indexPath.row == self.mEditIndex) {
            cell.isEditSelected = YES;
        }else{
            cell.isEditSelected = NO;
        }
    }
    
    return cell;
}

//- (void)configureDoseEditing:(id)cell forModel:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
//{
//    __weak typeof(self) weakSelf = self;
//    
//    [cell setDoseTextFieldDidBeginEditing:^{
//        [weakSelf handleDoseEditingBegin:medicineModel atIndexPath:indexPath];
//    }];
//    
//    [cell setDoseTextFieldDidEndEditing:^NSString *(NSString *doseStr){
//        return [weakSelf handleDoseEditingEnd:doseStr forModel:medicineModel atIndexPath:indexPath];
//    }];
//    
//    __weak typeof(cell) weakCell = cell;
//    [cell setDoseTextFieldShouldReturn:^NSString *(NSString *doseStr){
//        return [weakSelf handleDoseReturn:doseStr forModel:medicineModel atIndexPath:indexPath];
//    }];
//    
//    [cell setTextFieldEditingChanged:^(NSString *dose) {
//        [weakSelf handleDoseChange:dose forModel:medicineModel];
//        
//        //更新更改的cell数据显示
//        if ([weakCell isKindOfClass:[BRPresDrugCell class]]) {
//            [weakCell calculateDoseTotalPrice];
//        }
//        
//        if ([weakCell isKindOfClass:[BRPresHaveNoticeCell class]]) {
//            [weakCell calculateDoseTotalPrice];
//        }
//    }];
//}

- (void)configureDoseEditing:(id)cell forModel:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
{
    __weak typeof(self) weakSelf = self;
    
    // 使用 drugId 作为药材的唯一标识
    NSString *drugId = medicineModel.drugId;
    
    [cell setDoseTextFieldDidBeginEditing:^{
        // 获取当前药材的实时索引
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        if(currentIndex != NSNotFound) {
            NSIndexPath *currentIndexPath = [NSIndexPath indexPathForRow:currentIndex inSection:0];
            [weakSelf handleDoseEditingBegin:medicineModel atIndexPath:currentIndexPath];
        }
    }];
    
    [cell setDoseTextFieldDidEndEditing:^NSString *(NSString *doseStr){
        // 获取当前药材的实时索引
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        if(currentIndex != NSNotFound) {
            NSIndexPath *currentIndexPath = [NSIndexPath indexPathForRow:currentIndex inSection:0];
            return [weakSelf handleDoseEditingEnd:doseStr forModel:medicineModel atIndexPath:currentIndexPath];
        }
        return nil;
    }];
    
    __weak typeof(cell) weakCell = cell;
    [cell setDoseTextFieldShouldReturn:^NSString *(NSString *doseStr){
        // 获取当前药材的实时索引
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        if(currentIndex != NSNotFound) {
            NSIndexPath *currentIndexPath = [NSIndexPath indexPathForRow:currentIndex inSection:0];
            return [weakSelf handleDoseReturn:doseStr forModel:medicineModel atIndexPath:currentIndexPath];
        }
        return nil;
    }];
    
    [cell setTextFieldEditingChanged:^(NSString *dose) {
        [weakSelf handleDoseChange:dose forModel:medicineModel];
        
        //更新更改的cell数据显示
        if ([weakCell isKindOfClass:[BRPresDrugCell class]]) {
            [weakCell calculateDoseTotalPrice];
        }
        
        if ([weakCell isKindOfClass:[BRPresHaveNoticeCell class]]) {
            [weakCell calculateDoseTotalPrice];
        }
    }];
}

- (void)handleDoseEditingBegin:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
{
    [self.presToolBar setClearButtonAndPasteButtonHidden];
    [self.inputView showDoseViewWithProductSubType:self.subFactoryModel.productSubType];
    
    [self updateInputViewConstraintsForModel:medicineModel atIndexPath:indexPath];
    [self showSpecialUnitDescriptionForModel:medicineModel];
}

- (NSString *)handleDoseEditingEnd:(NSString *)doseStr forModel:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
{
    [self.presToolBar setClearButtonAndPasteButtonDisplay];
    [self.inputView showDrugView];
    [self.inputView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(78);
    }];
    
    NSString *dose = [doseStr stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    if (dose.length == 0) {
        dose = @"0";
    }
    
    if (![self isValidDose:dose forModel:medicineModel]) {
        [self changeDoseWithType:TEXT_FIELD_END_EDIT indexPath:indexPath dose:@"0" medicineModel:medicineModel ifRequestAssociate:NO];
        return @"0";
    }
    
    [self changeDoseWithType:TEXT_FIELD_END_EDIT indexPath:indexPath dose:dose medicineModel:medicineModel ifRequestAssociate:YES];
    return nil;
}

- (NSString *)handleDoseReturn:(NSString *)doseStr forModel:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
{
    NSString *dose = [doseStr stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    if ([dose floatValue] == 0) {
        [self.collectionView makeToast:@"剂量不能为0" duration:kToastDuration position:CSToastPositionCenter];
        [self changeDoseWithType:TEXT_FIELD_SHOULD_RETURN indexPath:indexPath dose:@"0" medicineModel:medicineModel ifRequestAssociate:NO];
        return @"0";
    }
    
    if (![self isValidDose:dose forModel:medicineModel]) {
        [self.collectionView makeToast:@"请按提示输入剂量" duration:kToastDuration position:CSToastPositionCenter];
        [self changeDoseWithType:TEXT_FIELD_SHOULD_RETURN indexPath:indexPath dose:@"0" medicineModel:medicineModel ifRequestAssociate:NO];
        return @"0";
    }
    
    [self changeDoseWithType:TEXT_FIELD_SHOULD_RETURN indexPath:indexPath dose:dose medicineModel:medicineModel ifRequestAssociate:YES];
    
    [self.presToolBar setClearButtonAndPasteButtonDisplay];
    [self.inputView showDrugView];
    [self.inputView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(78);
    }];
    
    return nil;
}

- (void)handleDoseChange:(NSString *)dose forModel:(BRSubMedicineModel *)medicineModel
{
    medicineModel.dose = dose;
    [self calculateMultipleDrugPrice];
}

- (BOOL)isValidDose:(NSString *)dose forModel:(BRSubMedicineModel *)medicineModel
{
    if (![medicineModel.minSaleUnit isEqualToString:@""] && [self.factoryModel.drugFormName isEqualToString:@"颗粒"]) {
        NSDecimalNumber *doseDecimal = [NSDecimalNumber decimalNumberWithString:dose];
        NSDecimalNumber *minSaleUnitDecimal = [NSDecimalNumber decimalNumberWithString:medicineModel.minSaleUnit];
        NSDecimalNumber *multipleDecimal = [doseDecimal decimalNumberByDividingBy:minSaleUnitDecimal];
        
        return [multipleDecimal integerValue] == [multipleDecimal floatValue];
    }
    return YES;
}

//- (void)requestUseTypeForDrugWithId:(NSString *)drugId atIndexPath:(NSIndexPath *)indexPath
//{
//    if (![drugId isEqualToString:@""] && ([self.factoryModel.drugFormName isEqualToString:@"饮片"] || [self.factoryModel.drugFormName isEqualToString:@"代煎"])) {
//        [self requestUseTypeWithIndexPath:indexPath];
//    }
//}


- (void)updateInputViewConstraintsForModel:(BRSubMedicineModel *)medicineModel atIndexPath:(NSIndexPath *)indexPath
{
    if (![medicineModel.minSaleUnit isEqualToString:@""] &&
        (![medicineModel.minSaleUnit isEqualToString:@"1"] &&
         ![medicineModel.minSaleUnit isEqualToString:@"0.1"] &&
         ![medicineModel.minSaleUnit isEqualToString:@"0.01"])) {
        
        [self.inputView mas_updateConstraints:^(MASConstraintMaker *make) {
            if ([self.factoryModel.drugFormName isEqualToString:@"颗粒"]) {
                make.height.mas_equalTo(70);
            } else {
                make.height.mas_equalTo(0);
            }
        }];
        
        if ([self.factoryModel.drugFormName isEqualToString:@"颗粒"]) {
            [self.inputView setMedicineModel:medicineModel indexPath:indexPath];
            NSString *descStr = [NSString stringWithFormat:@"%@:包装规格为%@%@,剂量请输入%@%@的倍数",
                                 medicineModel.drugName,
                                 medicineModel.minSaleUnit,
                                 medicineModel.unit,
                                 medicineModel.minSaleUnit,
                                 medicineModel.unit];
            self.inputView.descLabel.text = descStr;
        } else {
            [self.inputView setMedicineModel:medicineModel indexPath:indexPath];
        }
    } else {
        [self.inputView.doseScrollView removeAllSubviews];
        
        [self.inputView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
    }
}

- (void)showSpecialUnitDescriptionForModel:(BRSubMedicineModel *)medicineModel
{
    if ([medicineModel.specialUnit count] != 0) {
        NSString *remark = [medicineModel.specialUnit valueForKey:@"remark"];
        if (remark.length == 0) {
            return;
        }

        [self.inputView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(70);
        }];

        NSString *descStr;
        if ([medicineModel.specialUnit objectForKey:@"remark"] == 0) {
            descStr = [NSString stringWithFormat:@"%@：%@约%@",
                       medicineModel.drugName,
                       [medicineModel.specialUnit objectForKey:@"unit"],
                       [medicineModel.specialUnit objectForKey:@"dose"]];
        } else {
            descStr = [NSString stringWithFormat:@"%@：%@",
                       medicineModel.drugName,
                       [medicineModel.specialUnit objectForKey:@"remark"]];
        }
        self.inputView.descLabel.text = descStr;
    }
}

- (BOOL)collectionView:(UICollectionView *)collectionView canMoveItemAtIndexPath:(NSIndexPath *)indexPath {
    return ![Config getLongPressActionType];
}

- (void)collectionView:(UICollectionView *)collectionView moveItemAtIndexPath:(NSIndexPath *)sourceIndexPath toIndexPath:(NSIndexPath *)destinationIndexPath{
    
    //更新数据源
    BRSubMedicineModel *model = [self.drugArray objectAtIndex:sourceIndexPath.item];
    [self.drugArray removeObjectAtIndex:sourceIndexPath.item];
    [self.drugArray insertObject:model atIndex:destinationIndexPath.item];
}

// 更新字体大小时的全局刷新
- (void)updateFontSize {
    // 获取新的字体大小比例
    CGFloat scale = [Config getFontSizeScale];
//    if (self.isEntryCommonPrescription) {
//        scale = 1.0;
//    }
    
    // 更新 collection view 的布局
    UICollectionViewFlowLayout *layout = (UICollectionViewFlowLayout *)self.collectionView.collectionViewLayout;
    layout.itemSize = CGSizeMake(self.isSingleShow ? SCREEN_WIDTH : SCREEN_WIDTH/2, 50 * scale);
    
    // 刷新所有 cell
    [self.collectionView performBatchUpdates:^{
        NSArray *visibleCells = [self.collectionView visibleCells];
        for (UICollectionViewCell *cell in visibleCells) {
            if ([cell isKindOfClass:[BRPresHaveNoticeCell class]]) {
                [(BRPresHaveNoticeCell *)cell updateFontSize];
            } else if ([cell isKindOfClass:[BRPresDrugCell class]]) {
                [(BRPresDrugCell *)cell updateFontSize];
            }
        }
    } completion:^(BOOL finished) {
        [self.collectionView.collectionViewLayout invalidateLayout];
    }];
}

#pragma mark ---- UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CGFloat scale = [Config getFontSizeScale];
    
    if (self.isSingleShow) {
        return (CGSize){SCREEN_WIDTH, 50 * scale};
    }else{
        return (CGSize){SCREEN_WIDTH/2, 50 * scale};
    }
}


- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
{
    return UIEdgeInsetsMake(0, 0, 0, 0);
}


- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section
{
    return 0.f;
}


- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section
{
    return 0.f;
}


#pragma mark ---- UICollectionViewDelegate

- (BOOL)collectionView:(UICollectionView *)collectionView shouldHighlightItemAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

// 点击高亮
- (void)collectionView:(UICollectionView *)collectionView didHighlightItemAtIndexPath:(NSIndexPath *)indexPath
{
    UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
    cell.backgroundColor = [UIColor whiteColor];
}


#pragma mark 点击单个药材
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    
    
}


// 长按某item，弹出copy和paste的菜单
- (BOOL)collectionView:(UICollectionView *)collectionView shouldShowMenuForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return NO;
}


/**
 滚动到最底部
 */
- (void)collectionViewScrollToBottom {
    
    /*
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:_drugArray.count-1 inSection:0];
        [_collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
    });
     */
    dispatch_async(dispatch_get_main_queue(), ^{
        
        UICollectionView *collectionView = self.collectionView;
        CGFloat contentHeight = collectionView.collectionViewLayout.collectionViewContentSize.height;
        CGFloat boundsHeight = collectionView.bounds.size.height;
        
        if (!(contentHeight >= 0 && contentHeight < CGFLOAT_MAX && boundsHeight >= 0)) {
            return;
        }
        
        CGFloat offsetY = MAX(-collectionView.contentInset.top, contentHeight - boundsHeight + collectionView.contentInset.bottom);
        CGPoint contentOffset = CGPointMake(collectionView.contentOffset.x, offsetY);
        
        [collectionView setContentOffset:contentOffset animated:YES];
        
    });
    
}

- (void)collectionViewReloadData {
    
    if (_drugArray.count > 0) {
//        [_presToolBar.clearButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
        _presToolBar.clearButtonColor = [UIColor br_textBlueColor];
    }
    else {
//        [_presToolBar.clearButton setTitleColor:[UIColor br_textMediumGrayColor] forState:UIControlStateNormal];
        _presToolBar.clearButtonColor = [UIColor br_textMediumGrayColor];
    }
    
    //计算药价
    [self calculateMultipleDrugPrice];
    
    //计算一共多少味药
    _presToolBar.drugNumStr = [NSString stringWithFormat:@"/共%ld味",_drugArray.count];
    
    [_collectionView reloadData];
    [self collectionViewScrollToBottom];
    
}

/**
 插入药材

 @param indexPaths indexPaths
 */
- (void)collectionViewInsertItemsAtIndexPaths:(NSArray<NSIndexPath *> *)indexPaths {
    
    if (_drugArray.count > 0) {
//        [_presToolBar.clearButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
        _presToolBar.clearButtonColor = [UIColor br_textBlueColor];
    }
    else {
//        [_presToolBar.clearButton setTitleColor:[UIColor br_textMediumGrayColor] forState:UIControlStateNormal];
        _presToolBar.clearButtonColor = [UIColor br_textMediumGrayColor];
    }
    
    //计算共有多少味药材
    _presToolBar.drugNumStr = [NSString stringWithFormat:@"/共%ld味",_drugArray.count];
    
//    [_collectionView insertItemsAtIndexPaths:indexPaths];
    [_collectionView reloadData];
    
}

#pragma mark - UICollectionViewItem -- 长按事件处理--编辑模式

- (void)handleLongPress:(UILongPressGestureRecognizer *)gestureRecognizer {
    CGPoint point = [gestureRecognizer locationInView:self.collectionView];
    NSIndexPath *indexPath = [self.collectionView indexPathForItemAtPoint:point];
    
    //    switch(gestureRecognizer.state) {
    //        case UIGestureRecognizerStateBegan: {
    //            [self showEditModePopWithIndex:indexPath.row];
    //            break;
    //        }
    //        case UIGestureRecognizerStateEnded:
    //            break;
    //        default:
    //            break;
    //    }
    
    BOOL isPopUp = [Config getLongPressActionType];
    
    if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
        if (!isPopUp) {
            // Initiate drag-and-drop
            [self.collectionView beginInteractiveMovementForItemAtIndexPath:indexPath];
        } else {
            // Show popup
            [self showEditModePopWithIndex:indexPath.row];
        }
    } else if (gestureRecognizer.state == UIGestureRecognizerStateChanged) {
        if (!isPopUp) {
            [self.collectionView updateInteractiveMovementTargetPosition:point];
        }
    } else if (gestureRecognizer.state == UIGestureRecognizerStateEnded) {
        if (!isPopUp) {
            [self.collectionView endInteractiveMovement];
        }
    } else {
        if (!isPopUp) {
            [self.collectionView cancelInteractiveMovement];
        }
    }
}


#pragma mark- 编辑模式相关处理
- (void)showEditModePopWithIndex:(NSInteger)cellIndex {
    
    [self.view endEditing:YES];
    
    _chooseEditModeActionSheet = [[BRActionSheetView alloc] init];
    _chooseEditModeActionSheet.title = @"编辑模式";
    _chooseEditModeActionSheet.buttons = @[@"向前添加",@"向后添加",@"替换药味",@"正常添加"];
//    _chooseEditModeActionSheet.bottomTitle = @"正常";
    [_chooseEditModeActionSheet show];
    
    __weak AddDrugViewController *weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = _chooseEditModeActionSheet;
//    _chooseEditModeActionSheet.clickBottomButtonCallBack = ^{
//        [weakActionSheet close];
//        
//        //恢复正常模式
//        [weakSelf changeToNormalEditMode];
//        
//        [weakSelf.collectionView reloadData];
//    };
    
    _chooseEditModeActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        if (index == 3) {
            //恢复到正常模式
            [weakSelf changeToNormalEditMode];
            [weakSelf.collectionView reloadData];
            
        } else {
            weakSelf.mEditIndex = cellIndex;
            
            if (index == 0) {
                //向前插入
                weakSelf.mEditmode = EditModeForward;
            }else if (index == 1){
                //向后插入
                weakSelf.mEditmode = EditModeBackward;
            }else if (index == 2){
                //替换
                weakSelf.mEditmode = EditModeReplace;
            }
            [weakSelf.collectionView reloadData];
        }
    };
    
}

//恢复正常编辑模式
- (void)changeToNormalEditMode {
    self.mEditmode = EditModeNormal;
    self.mEditIndex = -1;
}


#pragma mark- 剂量修改的问题

/**
 修改剂量

 @param type 键盘return键有关
 @param indexPath indexPath
 @param doseStr 剂量
 @param medicineModel 药材的model
 @param ifRequestAssociate 是否请求联想
 */
- (void)changeDoseWithType:(NSString *)type indexPath:(NSIndexPath *)indexPath dose:(NSString *)doseStr medicineModel:(BRSubMedicineModel *)medicineModel ifRequestAssociate:(BOOL)ifRequestAssociate{

//    if ([type isEqualToString:TEXT_FIELD_SHOULD_RETURN] && (![doseStr isEqualToString:medicineModel.dose] || [doseStr isEqualToString:@"0"])) {
    if ([type isEqualToString:TEXT_FIELD_SHOULD_RETURN]) {
        
        if (ifRequestAssociate) {
            [_inputView.inputField becomeFirstResponder];
            [self requestAssociateDataWithDrugId:medicineModel.drugId];
        }
        
    }
    else {
        //药品展示置空
        [_inputView searchResultArrayNone];
    }
    
    medicineModel.dose = doseStr;
    [_drugArray replaceObjectAtIndex:indexPath.row withObject:medicineModel];
    [self calculateMultipleDrugPrice];
    
    _inputView.inputField.text = @"";
    
}

//按倍改量
- (void)changeDoseByMultipleWithFactor:(NSDecimalNumber *)factorNumber {
    // 根据倍数更改所有添加的药材的剂量
    for (int i = 0; i < _drugArray.count; i++) {
        BRSubMedicineModel *medicineModel = [_drugArray objectAtIndex:i];
        NSDecimalNumber *doseDecimal = [NSDecimalNumber decimalNumberWithString:medicineModel.dose];
        
        // 将 doseDecimal 和 factorNumber 相乘
        NSDecimalNumber *result = [doseDecimal decimalNumberByMultiplyingBy:factorNumber];
        
        // 设置舍入行为为四舍五入
        NSDecimalNumberHandler *roundingBehavior = [NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundPlain
                                                                                                          scale:0
                                                                                               raiseOnExactness:NO
                                                                                                raiseOnOverflow:NO
                                                                                               raiseOnUnderflow:NO
                                                                                            raiseOnDivideByZero:NO];
        
        // 对结果进行四舍五入
        NSDecimalNumber *roundedResult = [result decimalNumberByRoundingAccordingToBehavior:roundingBehavior];
        
        // 将结果转换为整数字符串
        NSString *newDose = [roundedResult stringValue];
        
        // 更新药材模型的剂量
        medicineModel.dose = newDose;
    }
    
    //更新列表价格
    [self.collectionView reloadData];
    
    //更新总价
    [self calculateMultipleDrugPrice];
}

#pragma mark- 请求药材特殊煎法
//- (void)requestUseTypeWithIndexPath:(NSIndexPath *)indexPath {
// 修改: 方法名和参数，改为使用 drugId
- (void)requestUseTypeWithDrugId:(NSString *)drugId {
    // 参数检查
    if (!drugId || drugId.length == 0) {
        return;
    }
    
    // 检查是否支持特殊煎法（只有饮片和代煎支持）
    if (![self.factoryModel.drugFormName isEqualToString:@"饮片"] &&
        ![self.factoryModel.drugFormName isEqualToString:@"代煎"]) {
        return;
    }
    
    if (_baseUseTypeArr.count != 0) {
        // 已有煎法数据，直接显示选择界面
        NSMutableArray *totalUseTypeArr = [[NSMutableArray alloc] init];
        [totalUseTypeArr addObjectsFromArray:_useTypeArr];
        [totalUseTypeArr addObjectsFromArray:_baseUseTypeArr];
        
        [self showUseTypeActionSheetWithUseTypeArr:totalUseTypeArr drugId:drugId];
        return;
    }
    
    // 需要请求特殊煎药方式
    __weak typeof(self) weakSelf = self;
    [self updateBoilyWayFromServerWithBlock:^(NSMutableArray *totalUseTypeArr, NSString *errorMsg) {
        if (totalUseTypeArr) {
            [weakSelf showUseTypeActionSheetWithUseTypeArr:totalUseTypeArr drugId:drugId];
        } else {
            [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
        }
    }];
}

- (void)updateBoilyWayFromServerWithBlock:(UpdateBoilyWayBlock)block  {
    
    //请求药材特殊煎法
    NSDictionary *dict = @{@"method_code":@"000237"};
    
    __weak AddDrugViewController *weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            
            weakSelf.baseUseTypeArr = [NSArray arrayWithArray:[responseObject objectForKey:@"decoction1"]];
            weakSelf.useTypeArr = [NSMutableArray arrayWithArray:[responseObject objectForKey:@"decoction2"]];
            
            // 为系统煎法名字后面添加"*"标记
            NSMutableArray *markedBaseUseTypeArr = [NSMutableArray array];
            for (NSString *boilyWay in weakSelf.baseUseTypeArr) {
                [markedBaseUseTypeArr addObject:[NSString stringWithFormat:@"%@*", boilyWay]];
            }
            weakSelf.baseUseTypeArr = markedBaseUseTypeArr;
            
            NSMutableArray *totalUseTypeArr = [[NSMutableArray alloc]init];
            [totalUseTypeArr addObjectsFromArray:weakSelf.useTypeArr];
            [totalUseTypeArr addObjectsFromArray:weakSelf.baseUseTypeArr];
            
//            [weakSelf showUseTypeActionSheetWithUseTypeArr:totalUseTypeArr indexPath:indexPath];
            if (block) {
                block(totalUseTypeArr, nil);
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
            if (block) {
                block(nil, errorMsgStr);
            }
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
//        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
        if (block) {
            block(nil, @"请求失败，请求稍后再试");
        }
    }];
    
    
}


/**
 显示选择特殊煎法的view

 @param useTypeArr 特殊煎法集合
 */
// 修改: 相应的显示选择界面方法
- (void)showUseTypeActionSheetWithUseTypeArr:(NSArray *)useTypeArr drugId:(NSString *)drugId {
    [self.view endEditing:YES];
    
    // 通过 drugId 找到对应的药材模型
    BRSubMedicineModel *model = nil;
    for (BRSubMedicineModel *drugModel in self.drugArray) {
        if ([drugModel.drugId isEqualToString:drugId]) {
            model = drugModel;
            break;
        }
    }
    
    if (!model) {
        return;
    }
    
    _useTypeActionSheet = [[BRActionSheetView alloc] init];
    _useTypeActionSheet.title = [NSString stringWithFormat:@"%@", model.drugName];
    _useTypeActionSheet.buttons = useTypeArr;
    _useTypeActionSheet.bottomTitle = @"自定义";
    
    // 添加一个问号图标按钮
    UIButton *questionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [questionButton setImage:[UIImage imageNamed:@"question_mark_icon"] forState:UIControlStateNormal];
    questionButton.frame = CGRectMake(0, 0, 25, 25); // 设置按钮大小
    [questionButton addTarget:self action:@selector(showUseTypeHelpInfo) forControlEvents:UIControlEventTouchUpInside];
    
    // 设置问号图标按钮
    _useTypeActionSheet.rightButton = questionButton;
    
    [_useTypeActionSheet show];
    
    __weak typeof(self) weakSelf = self;
    __weak BRActionSheetView *weakActionSheet = _useTypeActionSheet;
    
    // 点击底部"自定义"按钮的回调
    _useTypeActionSheet.clickBottomButtonCallBack = ^{
        [weakActionSheet close];
        
        CustomBoilyWayViewController *customBoilyWayVC = [[CustomBoilyWayViewController alloc] init];
        [weakSelf.navigationController pushViewController:customBoilyWayVC animated:YES];
        
        customBoilyWayVC.updateBoilywayBlock = ^{
            [weakSelf updateBoilyWayFromServerWithBlock:^(NSMutableArray *totalUseTypeArr, NSString *errorMsg) {
                // 更新完成后不需要做其他操作
            }];
        };
    };
    
    // 选择特殊煎法的回调
    _useTypeActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        [weakSelf.inputView.inputField becomeFirstResponder];
        
        // 获取完整的煎法列表
        NSMutableArray *totalUseTypeArr = [[NSMutableArray alloc] init];
        [totalUseTypeArr addObjectsFromArray:weakSelf.useTypeArr];
        [totalUseTypeArr addObjectsFromArray:weakSelf.baseUseTypeArr];
        
        // 根据drugId找到当前药材
        NSInteger currentIndex = [weakSelf findIndexForDrugId:drugId];
        if (currentIndex == NSNotFound) {
            return;
        }
        
        BRSubMedicineModel *currentModel = [weakSelf.drugArray objectAtIndex:currentIndex];
        
        // 设置选中的煎法
        NSString *useMethod = [totalUseTypeArr objectAtIndex:index];
        // 移除可能存在的"*"标记
        if ([useMethod hasSuffix:@"*"]) {
            useMethod = [useMethod substringToIndex:useMethod.length - 1];
        }
        currentModel.useMethod = [useMethod isEqualToString:@"无"] ? @"" : useMethod;
        
        // 更新数据源
        [weakSelf.drugArray replaceObjectAtIndex:currentIndex withObject:currentModel];
        
        // 刷新对应的cell
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:currentIndex inSection:0];
        [weakSelf.collectionView reloadItemsAtIndexPaths:@[indexPath]];
    };
}

// 显示特殊煎法帮助信息
- (void)showUseTypeHelpInfo {
    // 显示提示语
    [Utils br_showTipsInfoWithTitle:@"特殊煎法说明" message:@"标有【*】的为系统默认，不可修改" btnTitle:@"知道了" handleBlock:^{
        // 点击确定后的回调，这里不需要做任何事情
    }];
}

#pragma mark 切换剂型
- (void)pressToChangeMode {
    
    [self.view endEditing:YES];
    
    if (self.factoryArray.count > 0 && self.drugArray.count == 0) {
        [self showSelectTypeActionSheetViewWithFactoryArr:_factoryArray];
    }
    else {
        [self getFactoryInfoWithType:@"pressAddPrescription"];
    }
}

#pragma mark 弹出选择剂型
- (void)showSelectTypeActionSheetViewWithFactoryArr:(NSArray *)factoryArr {
    
    _selectTypeView = [[BRPresSelectTypeView alloc]init];
    _selectTypeView.masterSelectedIndex = _masterSelectedIndex;
    _selectTypeView.detailSelectedIndex = _detailSelectedIndex;
    _selectTypeView.masterArr = [NSMutableArray arrayWithArray:factoryArr];
    _selectTypeView.isShowStockInfo = self.drugArray.count == 0 ? NO : YES;
    BRFactoryModel *model = [factoryArr objectAtIndex:_masterSelectedIndex];
    [_selectTypeView show];
    
    __weak AddDrugViewController *weakSelf = self;
    _selectTypeView.pressDetailRow = ^(NSInteger masterSelectedIndex,NSInteger detailSelectedIndex){
        
        [weakSelf.selectTypeView close];
        
        //清空搜索出来的内容
        [weakSelf.inputView setSearchResultArr:@[]];
        weakSelf.inputView.inputField.text = @"";
        
        weakSelf.factoryModel = [factoryArr objectAtIndex:masterSelectedIndex];
        NSArray *detailArr = weakSelf.factoryModel.list;
        weakSelf.subFactoryModel = [detailArr objectAtIndex:detailSelectedIndex];
        
        NSString *titleStr = [NSString stringWithFormat:@"%@(%@)",weakSelf.factoryModel.drugFormName,weakSelf.subFactoryModel.factoryName];
        
        [weakSelf.customTitleView removeFromSuperview];
        
        [weakSelf setTitleViewWithTitleStr:titleStr];
        
        weakSelf.masterSelectedIndex = masterSelectedIndex;
        weakSelf.detailSelectedIndex = detailSelectedIndex;
        
        
        [weakSelf hiddenAndInvalidateTimer];
        
        
        if ([weakSelf.factoryModel.drugFormName isEqualToString:@"饮片"] ||
            [weakSelf.factoryModel.drugFormName isEqualToString:@"代煎"]) {
            [weakSelf displayNoticeView];
            
        }
        
        if ([weakSelf.factoryModel.drugFormName isEqualToString:@"颗粒"]) {
            [weakSelf showGranuleNoticeView];
        }
        
        [weakSelf.inputView.inputField becomeFirstResponder];
        
        if (_drugArray.count == 0) {
            return ;
        }
        
        [weakSelf requestDrugsInfoWithDrugArr:weakSelf.drugArray messageId:@"replaceDrugs"];
        
        //处理药材
        //[weakSelf changeFactoryInfoWithDrugType:subModel];
        
    };
    
    __weak BRPresSelectTypeView *weakSelectTypeView = _selectTypeView;
    _selectTypeView.gotoInfo = ^(NSString *urlStr){
        
        [weakSelectTypeView close];
       
        FactoryInfoViewController *factoryInfoVC = [[FactoryInfoViewController alloc]init];
        factoryInfoVC.urlStr = urlStr;
        [weakSelf.navigationController pushViewController:factoryInfoVC animated:YES];
        
    };
    
}
//获取厂商信息
- (void)getFactoryInfoWithType:(NSString *)type {
    
    __weak AddDrugViewController *weakSelf = self;
    
    
    NSMutableArray *drugIdsArr = [[NSMutableArray alloc]initWithCapacity:0];
    for (BRSubMedicineModel *model in _drugArray) {
        [drugIdsArr addObject:model.drugId];
    }
    NSString *drugIdsString = [drugIdsArr componentsJoinedByString:@","];
    
    //请求药厂数据
    NSDictionary *dict = @{@"method_code":@"000255",
                           @"drugIds": drugIdsString,
                           @"apiVer" : @"2"
                           };
    
    MBProgressHUD *hud = nil;
    if (![type isEqualToString:@"viewWillAppear"]) {
        hud = [Utils createLoadingHUD];
    }
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if (![type isEqualToString:@"viewWillAppear"]) {
            [hud hideAnimated:YES];
        }
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSLog(@"选择药剂 == %@",responseObject);
            weakSelf.factoryArray = [BRFactoryModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]];
            
            for (NSInteger index=0; index<weakSelf.factoryArray.count; index++) {
                
                BRFactoryModel *facoryModel = [weakSelf.factoryArray objectAtIndex:index];
                NSArray *factoryArr = [BRSubFactoryModel mj_objectArrayWithKeyValuesArray:facoryModel.list];
                facoryModel.list = factoryArr;
                
                [weakSelf.factoryArray replaceObjectAtIndex:index withObject:facoryModel];
                
            }
            
            if ([type isEqualToString:@"pressAddPrescription"]) {
                [weakSelf showSelectTypeActionSheetViewWithFactoryArr:weakSelf.factoryArray];
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        if (![type isEqualToString:@"viewWillAppear"]) {
            [hud hideAnimated:YES];
        }
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark 获取多个药材的详细信息
- (void)requestDrugsInfoWithDrugArr:(NSArray *)drugArr messageId:(NSString *)messageId{
    
    //先从网上请求最新的数据
    NSMutableArray *drugIdsArr = [[NSMutableArray alloc]init];
    for (BRSubMedicineModel *model in drugArr) {
        [drugIdsArr addObject:model.drugId];
    }
    NSString *drugIdsString = [drugIdsArr componentsJoinedByString:@","];
    [self requestDrugsInfoWithDrugIdString:drugIdsString messageId:messageId];
    
}

#pragma mark 请求药材详细数据
- (void)requestDrugsInfoWithDrugIdString:(NSString *)drugIdString messageId:(NSString *)messageId {
    
    //这里请求药品的详细数据
    
    
    NSMutableDictionary *paramDict = [NSMutableDictionary dictionaryWithDictionary:@{
        @"userid":[UserManager shareInstance].getUserId,
//        @"patientId":_patientId,
        @"drugId":drugIdString,
        @"drugType":_subFactoryModel.productSubType,
        @"drugForm":_factoryModel.drugFormName,
        @"drugProviderId":_subFactoryModel.factoryId,
        @"code":@"0033",
        @"sysType":@"2",
        @"appVer":[UIApplication sharedApplication].appVersion,
    }];
    
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [paramDict setValuesForKeysWithDictionary:@{
            @"patientId" : _patientId
        }];
    }
    
    
    NSDictionary *dict = @{
                           @"code":@"0033",
                           @"id":messageId,
                           @"params" : paramDict
//                           @"params":@{
//                                   @"userid":[UserManager shareInstance].getUserId,
//                                   @"patientId":_patientId,
//                                   @"drugId":drugIdString,
//                                   @"drugType":_subFactoryModel.productSubType,
//                                   @"drugForm":_factoryModel.drugFormName,
//                                   @"drugProviderId":_subFactoryModel.factoryId,
//                                   @"code":@"0033",
//                                   @"sysType":@"2",
//                                   @"appVer":[UIApplication sharedApplication].appVersion
//                                   }
                           };
    
    [[SocketManager shareInstance] sendDataDict:dict];
    
}

#pragma mark 切换剂型处方药材方式
- (void)changeFactoryInfoWithDrugType:(BRSubFactoryModel *)factoryModel {
    
    _factoryModel = [_factoryArray objectAtIndex:_masterSelectedIndex];
    NSArray *detailArr = _factoryModel.list;
    //没有改变之前的
    _subFactoryModel = [detailArr objectAtIndex:_detailSelectedIndex];
    
}

#pragma mark 显示与隐藏提示框
- (void)displayNoticeView {
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc]init];
    dateFormatter.dateFormat = @"yyyy-MM-dd";
    NSString *dateStr = [dateFormatter stringFromDate:[NSDate date]];
    
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *dateStrFromUF = [userDefaults objectForKey:@"pres_show_notice_date"];
    if ([dateStrFromUF isEqualToString:dateStr] ||
        (![_factoryModel.drugFormName isEqualToString:@"饮片"] &&
         ![_factoryModel.drugFormName isEqualToString:@"代煎"])) {
        [self hiddenAndInvalidateTimer];
        return;
    }
    else {
        [userDefaults setObject:dateStr forKey:@"pres_show_notice_date"];
    }
    
//    CGFloat height = [_presNoticeView.noticeLabel.text heightForFont:_presNoticeView.noticeLabel.font width:kScreenWidth-30];
    
    _presNoticeView.noticeStr = @"点击药名选择特殊煎药方式";
    _presNoticeView.hidden = NO;
    
    CGFloat height = [_presNoticeView getNoticeViewHeight];
    
    [_presNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(@(height));
    }];
    
    NSTimeInterval timeInterval = 5;
    _timer = [NSTimer scheduledTimerWithTimeInterval:timeInterval target:self selector:@selector(hiddenAndInvalidateTimer) userInfo:nil repeats:NO];
    
}


- (void)showGranuleNoticeView {
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc]init];
    dateFormatter.dateFormat = @"yyyy-MM-dd";
    NSString *dateStr = [dateFormatter stringFromDate:[NSDate date]];
    
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *dateStrFromUF = [userDefaults objectForKey:@"pres_show_granule_notice_date"];
    
    //如果手动关闭后，每天只显示一次
    if ([dateStr isEqualToString:dateStrFromUF] || ![_factoryModel.drugFormName isEqualToString:@"颗粒"]) {
        //隐藏提示
        [self hiddenAndInvalidateTimer];
        return;
    }
    
    //设置当前提示文字，并根据实际文字进行高度设置
    NSString *text = self.subFactoryModel.standardDesc;
    
    _presNoticeView.noticeStr = text;
    
    if ([text isEqualToString:@""]) {
        //空内容
        _presNoticeView.hidden = YES;
    }else{
        // 计算提示文本的高度
//        CGFloat height = [_presNoticeView.noticeLabel.text heightForFont:_presNoticeView.noticeLabel.font width:kScreenWidth-30];
        CGFloat height = [_presNoticeView getNoticeViewHeight];
        
        _presNoticeView.hidden = NO;
        [_presNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(@(height));
        }];
        
        
        //进行提示，并记录当前的时间，弹窗关闭的时候用来记录当天时间
        self.showNoticeTimeString = dateStr;
    }
}

//隐藏noticeview
- (void)hiddenAndInvalidateTimer {
    
    [self hiddenNoticeView];
    
    [self.timer invalidate];
    
}

- (void)hiddenNoticeView {
    
    _presNoticeView.hidden = YES;
    
    [_presNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(0);
    }];
    
}

#pragma mark 显示 长按显示处方详情
- (void)showPrescriptionInfoWithDrugArr:(NSArray *)drugArr presName:(NSString *)presName {
    
    [self.view endEditing:YES];
    
    _presInfoView = [[BRPresInfoView alloc]init];
    _presInfoView.drugArr = [NSMutableArray arrayWithArray:drugArr];
    _presInfoView.title = presName;
    _presInfoView.topButtonTitle = @"清空";
    _presInfoView.bottomTitle = @"确定";
    [_presInfoView show];
    
    __weak BRPresInfoView *weakPresInfoView = _presInfoView;
    __weak AddDrugViewController *weakSelf = self;
    _presInfoView.clickBottomButton = ^(NSMutableArray *selectDrugArr) {
        
        [weakPresInfoView close];
        
        [weakSelf insertDrugWithDrugArr:selectDrugArr];
        
    };
    
}

#pragma mark- socket delegate
- (void)socketManagerDidBusinessInfo:(NSString *)message {
    
    NSLog(@"message is %@",message);
    
    id messageObject = [message jsonValueDecoded];
    NSString *code = [messageObject objectForKey:@"code"];
    
    if ([code isEqualToString:@"0031"]) {
      
        NSDictionary *result = [messageObject objectForKey:@"result"];
        NSArray *drugArr = [BRSearchModel mj_objectArrayWithKeyValuesArray:[result objectForKey:@"data"]];
        
        [_inputView setSearchResultArr:drugArr];
        
    }
    else if ([code isEqualToString:@"0032"]) {
        
        NSDictionary *result = [messageObject objectForKey:@"result"];
        NSArray *drugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:[result objectForKey:@"data"]];
        
        NSString *messageId = [messageObject objectForKey:@"id"];
        NSString *touchType = [[messageId componentsSeparatedByString:@","] firstObject];
        NSString *presName = [[messageId componentsSeparatedByString:@","] lastObject];
        if ([touchType isEqualToString:@"unlongPress"]) {
            
            [self insertDrugWithDrugArr:drugArr];
            
        }
        else {
            
            [self showPrescriptionInfoWithDrugArr:drugArr presName:presName];
            
        }
        
    }
    else if ([code isEqualToString:@"0033"]) {
#pragma mark 获取单个药材详情
        [self.inputView hiddenMoreDrugView];
        
        NSDictionary *result = [messageObject objectForKey:@"result"];
        NSString *messageId = [messageObject objectForKey:@"id"];
        NSArray *drugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:[result objectForKey:@"data"]];
        
        if ([messageId isEqualToString:@"insertSingleDrug"]) {
            
            //直接添加一味药
            BRSubMedicineModel *drugModel = [drugArr firstObject];
            
            for (BRSubMedicineModel *model in _drugArray) {
                
                if ([model.drugId isEqualToString:drugModel.drugId]) {
                    
                    [_inputView makeToast:[NSString stringWithFormat:@"\"%@\"已存在，无需重复添加",drugModel.drugName] duration:2 position:CSToastPositionCenter];
                    
                    return;
                }
                
            }
            
            if (!drugModel) {
                return;
            }
            
            if (![_factoryModel.drugFormName isEqualToString:@"饮片"] &&
                ![_factoryModel.drugFormName isEqualToString:@"代煎"]) {
                drugModel.useMethod = @"";
            }
            

//            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:_drugArray.count-1 inSection:0];
            NSInteger arrayCount = self.drugArray.count;
            NSIndexPath *indexPath = nil;
            
            //根据编辑模式进行药材添加
            switch (self.mEditmode) {
                case EditModeNormal:
                {
                    //正常模式
                    [_drugArray addObject:drugModel];
                    indexPath = [NSIndexPath indexPathForRow:arrayCount inSection:0];
                }
                    break;
                case EditModeForward:
                {
                    // 先判断是否重复
                    for (BRSubMedicineModel *model in _drugArray) {
                        if ([model.drugId isEqualToString:drugModel.drugId]) {
                            [_inputView makeToast:[NSString stringWithFormat:@"\"%@\"已存在，无需重复添加",drugModel.drugName] duration:2 position:CSToastPositionCenter];
                            return;
                        }
                    }
                    
                    //向前插入
                    if (self.mEditIndex < arrayCount) {
                        [_drugArray insertObject:drugModel atIndex:self.mEditIndex];
                        indexPath = [NSIndexPath indexPathForRow:self.mEditIndex inSection:0];
                    }else{
                        //如果editmEditIndex无效，则添加到末尾
                        [self.drugArray addObject:drugModel];
                        indexPath = [NSIndexPath indexPathForRow:arrayCount inSection:0];
                    }
                }
                    break;
                case EditModeBackward:
                {
                    
                    // 先判断是否重复
                    for (BRSubMedicineModel *model in _drugArray) {
                        if ([model.drugId isEqualToString:drugModel.drugId]) {
                            [_inputView makeToast:[NSString stringWithFormat:@"\"%@\"已存在，无需重复添加",drugModel.drugName] duration:2 position:CSToastPositionCenter];
                            return;
                        }
                    }
                    
                    //向后插入
                    if (self.mEditIndex < arrayCount) {
                        [_drugArray insertObject:drugModel atIndex:self.mEditIndex+1];
                        indexPath = [NSIndexPath indexPathForRow:self.mEditIndex + 1 inSection:0];
                    }else{
                        //如果editmEditIndex无效，则添加到末尾
                        [self.drugArray addObject:drugModel];
                        indexPath = [NSIndexPath indexPathForRow:arrayCount inSection:0];
                    }
                }
                    break;
                case EditModeReplace:
                {
                    if (self.mEditIndex < arrayCount) {
                        //替换时不需要判断重复,因为是替换掉原有位置的药材
                        BRSubMedicineModel *oldModel = [_drugArray objectAtIndex:self.mEditIndex];
                        if([oldModel.drugId isEqualToString:drugModel.drugId]) {
                            [_inputView makeToast:@"不能替换为相同药材" duration:2 position:CSToastPositionCenter];
                            return;
                        }
                        //替换
                        [_drugArray replaceObjectAtIndex:self.mEditIndex withObject:drugModel];
                        indexPath = [NSIndexPath indexPathForRow:self.mEditIndex inSection:0];
                        //                        [self.collectionView reloadItemsAtIndexPaths:@[indexPath]];
                    }else{
                        // 替换位置无效时添加到末尾,需要检查重复
                        for (BRSubMedicineModel *model in _drugArray) {
                            if ([model.drugId isEqualToString:drugModel.drugId]) {
                                [_inputView makeToast:[NSString stringWithFormat:@"\"%@\"已存在，无需重复添加",drugModel.drugName] duration:2 position:CSToastPositionCenter];
                                return;
                            }
                        }
                        
                        [self.drugArray addObject:drugModel];
                        indexPath = [NSIndexPath indexPathForRow:arrayCount inSection:0];
                        //                        [self.collectionView insertItemsAtIndexPaths:@[indexPath]];
                    }
                }
                    break;
                default:
                    break;
            }
            
            [self collectionViewInsertItemsAtIndexPaths:@[indexPath]];
            
            [self collectionViewScrollToBottom];
            
            //获取刚添加的cell
            if ([drugModel.stock isEqualToString:@"0"] || drugModel.useMethod.length != 0) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    BRPresHaveNoticeCell *cell = (BRPresHaveNoticeCell *)[_collectionView cellForItemAtIndexPath:indexPath];
                    [cell.doseTextField becomeFirstResponder];
                });
                
            }
            else {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    BRPresDrugCell *cell = (BRPresDrugCell *)[_collectionView cellForItemAtIndexPath:indexPath];
                    [cell.doseTextField becomeFirstResponder];
                });
            }
            
            //添加完成之后编辑模式修改为正常模式
            self.mEditmode = EditModeNormal;
            self.mEditIndex = -1;
            
        }
        else {
#pragma mark 处理切换剂型后的药材以及粘贴的药材
        
            NSMutableArray *oldDrugArr;
            if ([messageId isEqualToString:@"replaceDrugs"]) {
                oldDrugArr = _drugArray;
                
                [self replaceDrugWithDrugArr:drugArr oldDrugArr:oldDrugArr messageId:messageId];
                
            }
            else {
                oldDrugArr = _pasteDrugArr;
                
                if (_drugArray.count == 0) {
                    [self replaceDrugWithDrugArr:drugArr oldDrugArr:oldDrugArr messageId:messageId];
                    return;
                }
                
                [self.view endEditing:YES];
                
                //恢复正常的编辑模式
                [self changeToNormalEditMode];
                [self.collectionView reloadData];
                
                __weak AddDrugViewController *weakSelf = self;
                //提示是选择“覆盖”还是“新增”
                BRAlertView *alertView = [[BRAlertView alloc] init];
                __weak BRAlertView *weakAlertView = alertView;
                [alertView.cancelButton setTitle:@"覆盖" forState:UIControlStateNormal];
                [alertView.okButton setTitle:@"新增" forState:UIControlStateNormal];
                [alertView showAlertViewWithCancelButton:@"列表中已添加药材，请选择“覆盖”还是“新增”？" completion:^(BOOL isOk) {
                    
                    /**
                     * 新增，如有重复药材，将重复药材过滤掉，保留原有手动添加的药材
                     * 覆盖，将列表中药材清空
                     */
                    
                    NSMutableArray *addArr = [[NSMutableArray alloc]initWithCapacity:0];
                    
                    if (!isOk) {
                        //覆盖
                        [_drugArray removeAllObjects];
                        [addArr addObjectsFromArray:drugArr];
                    }
                    else {
                        
                        //剔除相同的药材
                        for (NSInteger index=0; index<drugArr.count; index++) {
                            
                            BRSubMedicineModel *model = [drugArr objectAtIndex:index];
                            
                            BOOL isExist = NO;
                            for (BRSubMedicineModel *selectedModel in weakSelf.drugArray) {
                                
                                if ([selectedModel.drugId isEqualToString:model.drugId]) {
                                    
                                    isExist = YES;
                                    break;
                                }
                                
                            }
                            
                            if (!isExist) {
                                [addArr addObject:model];
                            }
                            
                        }
                        
                    }
                    
                    [weakSelf replaceDrugWithDrugArr:addArr oldDrugArr:oldDrugArr messageId:messageId];
                    
                    [alertView close];
                    
                }];
                
            }
            
            
            
        }
        
    }
    else {
        
        //获取联想数据
        NSDictionary *result = [messageObject objectForKey:@"result"];
        if (result.allKeys.count > 0) {
            
            NSArray *drugArr = [BRSearchModel mj_objectArrayWithKeyValuesArray:[result objectForKey:@"data"]];
            
            [_inputView setSearchResultArr:drugArr];
            
        }
        else {
            [_inputView setSearchResultArr:@[]];
        }
        
    }
    
}

- (void)insertDrugWithDrugArr:(NSArray *)drugArr {
    
    [self changeToNormalEditMode];
    
    [self AddDrugFromSelectDrugArr:drugArr];
                                             
    _inputView.inputField.text = @"";
    
}

#pragma mark- 处方模板 delegate

// 创建一个新的方法来处理剂量调整
//- (NSString *)adjustDose:(NSString *)originalDose {
//    NSDecimalNumber *dose = [NSDecimalNumber decimalNumberWithString:originalDose];
//    NSDecimalNumber *one = [NSDecimalNumber decimalNumberWithString:@"1"];
//    NSDecimalNumber *integerPart = [dose decimalNumberByRoundingAccordingToBehavior:[NSDecimalNumberHandler decimalNumberHandlerWithRoundingMode:NSRoundDown scale:0 raiseOnExactness:NO raiseOnOverflow:NO raiseOnUnderflow:NO raiseOnDivideByZero:YES]];
//    
//    if ([dose compare:one] == NSOrderedAscending) {
//        // 如果剂量小于1，返回1
//        return @"1";
//    } else {
//        // 否则，返回整数部分
//        return [integerPart stringValue];
//    }
//}


- (void)AddDrugFromSelectDrugArr:(NSArray *)selectDrugArr {
    
    [_inputView hiddenMoreDrugView];
    
    //修改为正常开方模式
    [self changeToNormalEditMode];
    [self.collectionView reloadData];
    
    if (_drugArray.count == 0) {
        [self addNewDrugFromSelectDrugArr:selectDrugArr];
        return;
    }
    
    __weak AddDrugViewController *weakSelf = self;
    
    //提示是选择“覆盖”还是“新增”
    BRAlertView *alertView = [[BRAlertView alloc] init];
    __weak BRAlertView *weakAlertView = alertView;
    [alertView.cancelButton setTitle:@"覆盖" forState:UIControlStateNormal];
    [alertView.okButton setTitle:@"新增" forState:UIControlStateNormal];
    [alertView showAlertViewWithCancelButton:@"列表中已添加药材，请选择“覆盖”还是“新增”？" completion:^(BOOL isOk) {
        
        /**
         * 新增，如有重复药材，将重复药材过滤掉，保留原有手动添加的药材
         * 覆盖，将列表中药材清空
         */
        
        if (!isOk) {
            //覆盖
            [_drugArray removeAllObjects];
        }
        
        [weakSelf addNewDrugFromSelectDrugArr:selectDrugArr];
        
        [alertView close];
        
    }];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf.inputView.inputField resignFirstResponder];
    });
    
}

//替换药材与粘贴药材
- (void)replaceDrugWithDrugArr:(NSArray *)drugArr oldDrugArr:(NSArray *)oldDrugArr messageId:(NSString *)messageId{
    
    NSMutableArray *needShowArr = [[NSMutableArray alloc]initWithCapacity:0];
    
    //更改剂型要重新获取一批药材
    for (NSInteger index=0; index<oldDrugArr.count; index++) {
        
        BRSubMedicineModel *selectedModel = [oldDrugArr objectAtIndex:index];
        for (BRSubMedicineModel *model in drugArr) {
            
            if ([model.drugId isEqualToString:selectedModel.drugId]) {
                
                if (model.minSaleUnit && model.minSaleUnit.length != 0) {
                    
                    NSDecimalNumberHandler *roundDown = [NSDecimalNumberHandler
                                                         decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                         scale:0  // 修改为0，确保得到整数倍
                                                         raiseOnExactness:NO
                                                         raiseOnOverflow:NO
                                                         raiseOnUnderflow:NO
                                                         raiseOnDivideByZero:YES];
                    
                    NSDecimalNumberHandler *roundDownHaveDot = [NSDecimalNumberHandler
                                                                decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                                scale:3
                                                                raiseOnExactness:NO
                                                                raiseOnOverflow:NO
                                                                raiseOnUnderflow:NO
                                                                raiseOnDivideByZero:YES];
                    
                    NSDecimalNumberHandler *newDoseRoundDown = [NSDecimalNumberHandler
                                                                decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                                scale:0
                                                                raiseOnExactness:NO
                                                                raiseOnOverflow:NO
                                                                raiseOnUnderflow:NO
                                                                raiseOnDivideByZero:YES];
                    
                    if (!selectedModel.dose || selectedModel.dose.length == 0) {
                        selectedModel.dose = @"0";
                    }
                    
                    NSDecimalNumber *minSaleUnit = [NSDecimalNumber decimalNumberWithString:model.minSaleUnit];
                    NSDecimalNumber *dose = [NSDecimalNumber decimalNumberWithString:selectedModel.dose];
                    NSDecimalNumber *times = [dose decimalNumberByDividingBy:minSaleUnit withBehavior:roundDown];  // 直接使用scale:0的roundDown来向上取整
                    
                    if ([times integerValue] == 0) {
                        times = [NSDecimalNumber decimalNumberWithString:@"1"];
                        [needShowArr addObject:selectedModel];
                    }
                    
                    NSDecimalNumber *timesHaveDot = [dose decimalNumberByDividingBy:minSaleUnit withBehavior:roundDownHaveDot];
                    NSDecimalNumber *newDose = [minSaleUnit decimalNumberByMultiplyingBy:times withBehavior:newDoseRoundDown];
                    if ([timesHaveDot floatValue] != [timesHaveDot integerValue]) {
                        [needShowArr addObject:selectedModel];
                    }
//                    //使用剂量调整，如果有小数，则进行按规则调整
//                    NSString *adjustdDose = [self adjustDose:newDose.stringValue];
                    
                    model.dose = [NSString stringWithFormat:@"%@",newDose];
                    
                }
                else {
                    model.dose = selectedModel.dose;
                }
                
                if ([_factoryModel.drugFormName isEqualToString:@"饮片"] ||
                    [_factoryModel.drugFormName isEqualToString:@"代煎"]) {
                    
                    if (selectedModel.useMethod) {
                        model.useMethod = selectedModel.useMethod;
                    }
                    
                }
                else {
                    model.useMethod = @"";
                }
                
                if ([messageId isEqualToString:@"replaceDrugs"]) {
                    [_drugArray replaceObjectAtIndex:index withObject:model];
                }
                else {
                    [_drugArray addObject:model];
                }
                
                break;
                
            }
            
        }
        
    }
    
    if (needShowArr.count > 0) {
        [_collectionView makeToast:@"部分药材已自动调整剂量" duration:2 position:CSToastPositionCenter];
    }
    
    if (![messageId isEqualToString:@"replaceDrugs"]) {
        [_pasteDrugArr removeAllObjects];
    }
    
    [self collectionViewReloadData];
    
}

//新增药材
- (void)addNewDrugFromSelectDrugArr:(NSArray *)selectDrugArr {
    
    NSMutableArray *needShowArr = [[NSMutableArray alloc]initWithCapacity:0];
    
    for (BRSubMedicineModel *selectModel in selectDrugArr) {
        
        BOOL isExist = NO;
        for (BRSubMedicineModel *model in _drugArray) {
            
            if ([model.drugId isEqualToString:selectModel.drugId]) {
                isExist = YES;
                break;
            }
            
        }
        if (!isExist) {
            [_drugArray addObject:selectModel];
            
            if (![_factoryModel.drugFormName isEqualToString:@"饮片"] &&
                ![_factoryModel.drugFormName isEqualToString:@"代煎"]) {
                selectModel.useMethod = @"";
            }
            
            if (selectModel.minSaleUnit && selectModel.minSaleUnit.length != 0) {
                
                NSDecimalNumberHandler *roundDown = [NSDecimalNumberHandler
                                                     decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                     scale:3
                                                     raiseOnExactness:NO
                                                     raiseOnOverflow:NO
                                                     raiseOnUnderflow:NO
                                                     raiseOnDivideByZero:YES];
                
                NSDecimalNumberHandler *roundDownHaveDot = [NSDecimalNumberHandler
                                                            decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                            scale:3
                                                            raiseOnExactness:NO
                                                            raiseOnOverflow:NO
                                                            raiseOnUnderflow:NO
                                                            raiseOnDivideByZero:YES];
                
                NSDecimalNumberHandler *newDoseRoundDown = [NSDecimalNumberHandler
                                                            decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                            scale:0
                                                            raiseOnExactness:NO
                                                            raiseOnOverflow:NO
                                                            raiseOnUnderflow:NO
                                                            raiseOnDivideByZero:YES];
                
                
                NSDecimalNumber *minSaleUnit = [NSDecimalNumber decimalNumberWithString:selectModel.minSaleUnit];
                NSDecimalNumber *dose = [NSDecimalNumber decimalNumberWithString:selectModel.dose];
                NSDecimalNumber *times = [dose decimalNumberByDividingBy:minSaleUnit withBehavior:roundDown];
                
                if ([times integerValue] == 0) {
                    times = [NSDecimalNumber decimalNumberWithString:@"1"];
                    [needShowArr addObject:selectModel];
                }
                
                NSDecimalNumber *timesHaveDot = [dose decimalNumberByDividingBy:minSaleUnit withBehavior:roundDownHaveDot];
                NSDecimalNumber *newDose = [minSaleUnit decimalNumberByMultiplyingBy:times withBehavior:newDoseRoundDown];
                if ([timesHaveDot floatValue] != [timesHaveDot integerValue]) {
                    [needShowArr addObject:selectModel];
                }
                
                //使用剂量调整，如果有小数，则进行按规则调整
//                NSString *adjustdDose = [self adjustDose:newDose.stringValue];
                selectModel.dose = [NSString stringWithFormat:@"%@",newDose];
                
                if ([selectModel.stock isEqualToString:@"0"]) {
                    selectModel.dose = @"0";
                }
                
            }
            else {
                selectModel.dose = @"0";
            }
            
        }
        
    }
    
    if (needShowArr.count >0) {
        [_collectionView makeToast:@"部分药材已自动调整剂量" duration:2 position:CSToastPositionCenter];
    }
    
    [self collectionViewReloadData];
    
}

#pragma mark 获取价格
- (void)requestCharge {
    
    //获取价钱
    
    NSMutableArray *preDetailList = [[NSMutableArray alloc]init];
    for (NSInteger index=0; index<_drugArray.count; index++) {
        
        BRSubMedicineModel *model = [_drugArray objectAtIndex:index];
        
        NSString *useMethod = @"";
        if (model.useMethod) {
            useMethod = model.useMethod;
        }
        
        NSString *description = @"";
        
        NSDictionary *specialUnit;
        if (model.specialUnit) {
            specialUnit = model.specialUnit;
        }
        else {
            specialUnit = (NSDictionary *)[NSNull null];
        }
        
        NSDictionary *dict = @{
                               @"dose":model.dose,
                               @"unit":model.unit,
                               @"useMethod":useMethod,
                               @"drugName":model.drugName,
                               @"description":description,
                               @"index":[NSString stringWithFormat:@"%ld",index],
                               @"drugId":model.drugId,
                               @"specialUnit":specialUnit
                               };
        
        [preDetailList addObject:dict];
        
    }
    
    NSMutableDictionary *orderDict = [NSMutableDictionary dictionaryWithDictionary:@{
        @"drugType":_subFactoryModel.productSubType,
        @"providerDrugformId":_subFactoryModel.providerDrugformId,
        @"providerId":_subFactoryModel.factoryId,
        @"userId":[UserManager shareInstance].getUserId,
//        @"patientId":_patientId,
        @"preDetailList":preDetailList
    }];
    
    if (self.prescribeType == BRPrescribeTypeNormal) {
        [orderDict setValuesForKeysWithDictionary:@{
            @"patientId":_patientId,
        }];
    }
    
    NSString *orderStr = [orderDict mj_JSONString];
    NSDictionary *dict  = @{
                            @"method_code":@"000275",
                            @"apiVersion":@"3",
                            @"order":orderStr
                            };
    
    __weak AddDrugViewController *weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            //制作费用
            NSString *makeCost = [responseObject objectForKey:@"makeCost"];
            NSString *drugPrice = [responseObject objectForKey:@"drugPrice"];
            //制作成后药材的重量
            NSString *balanceWeight = [responseObject objectForKey:@"balanceWeight"];
            //制作时间
            NSString *makeDays = [responseObject objectForKey:@"makeDays"];
            //制作描述
            NSString *makeDesc = [responseObject objectForKey:@"makeDesc"];
            
            //原始重量
            CGFloat weight = [[responseObject objectForKey:@"weight"] floatValue];
            
            if ([weakSelf.delegate respondsToSelector:@selector(addDrugListToPrescriptionWithDrugList:detailSelectedIndex:masterSelectedIndex:balanceWeight:drugPrice:makeCost:makeDays:makeDesc:weight:)]) {
                [weakSelf.delegate addDrugListToPrescriptionWithDrugList:_drugArray detailSelectedIndex:weakSelf.detailSelectedIndex masterSelectedIndex:weakSelf.masterSelectedIndex balanceWeight:balanceWeight drugPrice:drugPrice makeCost:makeCost makeDays:makeDays makeDesc:makeDesc weight:weight];
            }
            
            [weakSelf popViewController];
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark 改变工具条价格
- (void)calculateMultipleDrugPrice
{
    __block NSDecimalNumber *totalPrice = [NSDecimalNumber decimalNumberWithString:@"0"];
    
    dispatch_async(dispatch_queue_create("calculateDrugPriceQueue", NULL), ^{
        
        NSDecimalNumberHandler *roundUpHaveDot = [NSDecimalNumberHandler
                                                  decimalNumberHandlerWithRoundingMode:NSRoundUp
                                                  scale:3
                                                  raiseOnExactness:NO
                                                  raiseOnOverflow:NO
                                                  raiseOnUnderflow:NO
                                                  raiseOnDivideByZero:YES];
        
        for (BRSubMedicineModel *medicineModel in _drugArray) {
            
            if (medicineModel.price && medicineModel.price.length > 0 && medicineModel.dose && medicineModel.dose.length > 0){
                //单价处以0.7
//                NSDecimalNumber *recipelSalePriceDecimal = [[NSDecimalNumber decimalNumberWithString:medicineModel.price] decimalNumberByDividingBy:[NSDecimalNumber decimalNumberWithString:@"0.7"]];
                NSDecimalNumber *recipelSalePriceDecimal = [NSDecimalNumber decimalNumberWithString:medicineModel.price];
                
                recipelSalePriceDecimal = [recipelSalePriceDecimal decimalNumberByRoundingAccordingToBehavior:roundUpHaveDot];
                
                NSDecimalNumber *doseDecimal = [NSDecimalNumber decimalNumberWithString:medicineModel.dose];
                
                NSDecimalNumber *drugPriceDecimal = [recipelSalePriceDecimal decimalNumberByMultiplyingBy:doseDecimal];
                
                totalPrice = [totalPrice decimalNumberByAdding:drugPriceDecimal];
            }
        }
        
//        if ([[[UserManager shareInstance] getApp_ShowMedicalServiceFeeRule] isEqualToString:@"0"]) {
//            totalPrice = [[totalPrice decimalNumberByRoundingAccordingToBehavior:roundUpHaveDot] decimalNumberByDividingBy:[NSDecimalNumber decimalNumberWithString:@"0.7"]];
//        }
        
//        totalPrice = [totalPrice decimalNumberByRoundingAccordingToBehavior:roundUpHaveDot];
        NSMutableString *formatterString = [NSMutableString stringWithString:@"0.000"];
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        [formatter setPositiveFormat:formatterString];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            _presToolBar.priceStr = [NSString stringWithFormat:@"¥ %@",[formatter stringFromNumber:totalPrice]];
        });
        
    });
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

@end
