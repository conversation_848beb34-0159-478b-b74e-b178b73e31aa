//
//  BRPresUsageView.m
//  BRZY
//  开处方用法用量
//  Created by e<PERSON><PERSON> on 2017/10/13.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRPresUsageView.h"

#import "BRPrescriptionTitleView.h"
#import "MMNumberKeyboard.h"

@interface BRPresUsageView()<UITextFieldDelegate,MMNumberKeyboardDelegate>


@property (nonatomic, strong) BRPrescriptionTitleView *titleView;
@property (nonatomic, strong) UIButton *changeButton;
@property (nonatomic, strong) UIView *bgView;

//用药时间
@property (nonatomic, strong) UILabel *before_time_label;

//用药时间底部线条
@property (nonatomic, strong) UIView *usageBottomLineView;

@end

@implementation BRPresUsageView

- (instancetype)init
{
    self = [super init];
    if (self) {
        
        self.backgroundColor = [UIColor whiteColor];
        
        _drugType = BRDrugTypeKAndY;
        
        _titleView = [[BRPrescriptionTitleView alloc]initWithTitle:@"用法用量"];
        [self addSubview:_titleView];
        
        [_titleView mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.top.equalTo(self);
            make.left.and.right.equalTo(self);
            make.height.mas_equalTo(kTitleViewHeight);
            
        }];
        
        UIView *middleLine = [[UIView alloc]init];
        middleLine.frame = CGRectMake(0, 95, SCREEN_WIDTH, .5);
        middleLine.userInteractionEnabled = NO; // 禁用交互，避免拦截输入框的点击事件
        [self addSubview:middleLine];


        UIView *usageBottomLineView = [[UIView alloc] init];
        usageBottomLineView.frame = CGRectMake(0, 95, SCREEN_WIDTH, 0.5);
        usageBottomLineView.userInteractionEnabled = NO; // 禁用交互，避免拦截输入框的点击事件
        [self addSubview:usageBottomLineView];

        self.usageBottomLineView = usageBottomLineView;

        usageBottomLineView.hidden = YES;
        
        
        [ViewTools drawDashLine:middleLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
        
        [ViewTools drawDashLine:usageBottomLineView lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
        
        _bgView = [[UIView alloc]init];
        _bgView.backgroundColor = [UIColor whiteColor];
        [self addSubview:_bgView];
        
        [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.top.equalTo(self.titleView.mas_bottom);
            make.left.and.right.equalTo(self);
            make.height.mas_equalTo(50);
            
        }];
        
        //创建颗粒或者饮片的view
        [self buildYAndKTypeView];
        
#pragma mark 服用时间
       
        UILabel *before_time_label = [[UILabel alloc]init];
        before_time_label.text = @"用药时间";
        before_time_label.font = kFontLight(16);
        before_time_label.textColor = [UIColor br_textBlackColor];
        [self addSubview:before_time_label];
        
        _changeTimeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_changeTimeButton setTitle:@"饭后一小时" forState:UIControlStateNormal];
        [_changeTimeButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
        _changeTimeButton.titleLabel.font = kFontLight(17);
        [_changeTimeButton addTarget:self action:@selector(pressToChangeTime) forControlEvents:UIControlEventTouchUpInside];
        _changeTimeButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        [self addSubview:_changeTimeButton];
        
        _changeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_changeButton setImage:[UIImage imageNamed:@"prescription_change_time_big"] forState:UIControlStateNormal];
        [_changeButton addTarget:self action:@selector(pressToChangeTime) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:_changeButton];
        
        [before_time_label mas_makeConstraints:^(MASConstraintMaker *make) {

            make.left.equalTo(self.mas_left).offset(kHorizontalMargin);
            make.bottom.equalTo(self.mas_bottom).offset(-15); // 距离底部15像素，保持与其他行一致的间距
            make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
            make.width.mas_equalTo(80);

        }];
        
        self.before_time_label = before_time_label;
        
        
        [_changeTimeButton mas_makeConstraints:^(MASConstraintMaker *make) {

            make.right.equalTo(self.changeButton.mas_left).offset(-10);
            make.centerY.equalTo(before_time_label);
            make.height.mas_equalTo(30); // 保持30像素高度
            make.width.mas_equalTo(120);

        }];

        [_changeButton mas_makeConstraints:^(MASConstraintMaker *make) {

            make.right.equalTo(self).offset(-kHorizontalMargin);
            make.centerY.equalTo(before_time_label);
            make.height.and.width.mas_equalTo(30); // 保持30像素高度

        }];
        
        // 初始化默认值
//        _selectedMode = @"内服";
        _isSpecificationRowVisible = NO; // 默认隐藏规格行

        // 创建用药方法相关控件
        _usageMethodLabel = [[UILabel alloc] init];
        _usageMethodLabel.text = @"用药方法";
        _usageMethodLabel.font = kFontLight(16);
        _usageMethodLabel.textColor = [UIColor br_textBlackColor];
        _usageMethodLabel.hidden = YES;
        [self addSubview:_usageMethodLabel];
        
        _internalUseButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_internalUseButton setTitle:@"内服" forState:UIControlStateNormal];
        [_internalUseButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
        [_internalUseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [_internalUseButton addTarget:self action:@selector(usageMethodButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        _internalUseButton.layer.borderWidth = 0.5;
        _internalUseButton.layer.cornerRadius = 4;
        _internalUseButton.hidden = YES;
//        _internalUseButton.selected = YES;
        [self addSubview:_internalUseButton];
        
        _externalUseButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_externalUseButton setTitle:@"外用" forState:UIControlStateNormal];
        [_externalUseButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
        [_externalUseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [_externalUseButton addTarget:self action:@selector(usageMethodButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        _externalUseButton.layer.borderWidth = 0.5;
        _externalUseButton.layer.cornerRadius = 4;
        _externalUseButton.hidden = YES;
        [self addSubview:_externalUseButton];
        
        // 添加约束
        [_usageMethodLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(kHorizontalMargin);
            make.bottom.equalTo(self).offset(-15);
            make.width.mas_equalTo(80);
            make.height.mas_equalTo(20);
        }];
        
        [_externalUseButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(@-20);
            make.centerY.equalTo(_usageMethodLabel);
            make.width.mas_equalTo(60);
            make.height.mas_equalTo(30);
        }];
        
        [_internalUseButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.externalUseButton.mas_left).with.offset(-20);
            make.centerY.equalTo(_usageMethodLabel);
            make.width.mas_equalTo(60);
            make.height.mas_equalTo(30);
        }];
        
        [_usageBottomLineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.before_time_label.mas_bottom).with.offset(15);
            make.left.right.equalTo(self);
            make.height.mas_equalTo(0.5);
        }];
    }
    return self;
}


// 添加按钮点击事件处理
- (void)usageMethodButtonTapped:(UIButton *)sender {
//    if (sender == _internalUseButton) {
//        _internalUseButton.selected = YES;
//        _externalUseButton.selected = NO;
//        _selectedMode = @"内服";
//        [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
//    } else {
//        _internalUseButton.selected = NO;
//        _externalUseButton.selected = YES;
//        _selectedMode = @"外用";
//        [_internalUseButton setBackgroundColor:[UIColor whiteColor]];
//        [_externalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//    }
    if (sender == _internalUseButton) {
           _selectedMode = @"内服";
       } else {
           _selectedMode = @"外用";
       }
       [Utils saveUsageType:kUsageMethodKey text:_selectedMode withPatientId:self.patientId];
       
       // 更新UI
       _internalUseButton.selected = [_selectedMode isEqualToString:@"内服"];
       _externalUseButton.selected = [_selectedMode isEqualToString:@"外用"];
       [_internalUseButton setBackgroundColor:_internalUseButton.selected ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
       [_externalUseButton setBackgroundColor:_externalUseButton.selected ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
}


#pragma mark - 设置胶囊颗数
- (void)setCapsuleParticleNum:(NSString *)capsuleParticleNum {
    _capsuleParticleNum = capsuleParticleNum;
    
    if (capsuleParticleNum.length == 0) {
        self.capsuleParticleLabel.hidden = YES;
        self.capsuleParticleLabel.text = @"";
    }else{
        self.capsuleParticleLabel.hidden = NO;
        self.capsuleParticleLabel.text = [NSString stringWithFormat:@"(%@)",capsuleParticleNum];
    }
}

#pragma mark -
- (void)pressToChangeTime {
    
    _changeTime();
    
}

- (void)setDrugForm:(NSString *)drugForm {
    _drugForm = drugForm;
    
    // 1. 确定是否需要显示用药方法
    BOOL shouldShowUsageMethod = [self shouldShowUsageMethodForDrugForm:drugForm];
    [self updateUsageMethodVisibility:shouldShowUsageMethod];
    
    // 2. 调整视图高度
    [self updateViewHeightForDrugForm:drugForm shouldShowUsageMethod:shouldShowUsageMethod];
    
    // 3. 更新用药方式选择状态
    [self updateUsageModeSelection:shouldShowUsageMethod];
    
    // 4. 根据药品类型构建相应的视图
    if ([self isOtherTypeDrug:drugForm]) {
        [self buildAndRestoreOtherTypeView];
    } else {
        [self buildAndRestoreKAndYTypeView];
    }
}

#pragma mark - Helper Methods

- (BOOL)shouldShowUsageMethodForDrugForm:(NSString *)drugForm {
    return [drugForm isEqualToString:@"饮片"] ||
           [drugForm isEqualToString:@"代煎"] ||
           [drugForm isEqualToString:@"散剂"];
}

- (void)updateUsageMethodVisibility:(BOOL)shouldShow {
    _usageMethodLabel.hidden = !shouldShow;
    _internalUseButton.hidden = !shouldShow;
    _externalUseButton.hidden = !shouldShow;
}

- (void)updateViewHeightForDrugForm:(NSString *)drugForm shouldShowUsageMethod:(BOOL)shouldShow {
    if (!self.changeUsageHeight) {
        return;
    }
    
    CGFloat additionalHeight = shouldShow ? 50 : 0;
    if ([drugForm isEqualToString:@"散剂"]) {
        additionalHeight = additionalHeight + 50;
    }
    CGFloat baseHeight = kTitleViewHeight + 50 + 50;
    
    if ([self isOtherTypeDrug:drugForm]) {
        self.changeUsageHeight(baseHeight + 50 + additionalHeight);
    } else {
        //颗粒、饮片、代煎、外用中药
        self.changeUsageHeight(baseHeight + additionalHeight);
    }
}

- (void)updateUsageModeSelection:(BOOL)shouldShow {
    if (!shouldShow) {
        _selectedMode = @"";
        return;
    }
    
    //    _internalUseButton.selected = YES;
    //    _externalUseButton.selected = NO;
    //    _selectedMode = @"内服";
    //
    //    [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
    //    [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
    
    // 读取保存的用药方法
    NSString *savedMethod = [Utils getUsageType:kUsageMethodKey withPatientId:self.patientId];
    if (![savedMethod isEqualToString:@"-1"] && ![savedMethod isEqualToString:@""]) {
        // 恢复保存的状态
        _selectedMode = savedMethod;
        BOOL isInternal = [savedMethod isEqualToString:@"内服"];
        _internalUseButton.selected = isInternal;
        _externalUseButton.selected = !isInternal;
        [_internalUseButton setBackgroundColor:isInternal ? [UIColor br_mainBlueColor] : [UIColor whiteColor]];
        [_externalUseButton setBackgroundColor:isInternal ? [UIColor whiteColor] : [UIColor br_mainBlueColor]];
    } else {
        
        
        _internalUseButton.selected = NO;
        _externalUseButton.selected = NO;
        [_internalUseButton setBackgroundColor:[UIColor whiteColor]];
        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
        
        // 默认状态
//        _internalUseButton.selected = YES;
//        _externalUseButton.selected = NO;
//        _selectedMode = @"内服";
//        [_internalUseButton setBackgroundColor:[UIColor br_mainBlueColor]];
//        [_externalUseButton setBackgroundColor:[UIColor whiteColor]];
    }
    
}

- (BOOL)isOtherTypeDrug:(NSString *)drugForm {
    return ![drugForm isEqualToString:@"颗粒"] &&
           ![drugForm isEqualToString:@"饮片"] &&
           ![drugForm isEqualToString:@"代煎"] &&
           ![drugForm isEqualToString:@"外用中药"];
}

- (void)buildAndRestoreOtherTypeView {
    [self buildOtherTypeViewWithDrugForm:self.drugForm];
    
    NSString *drugFormMd5 = [[self.drugForm md5String] substringToIndex:5];
    
    if ([self.drugForm isEqualToString:@"散剂"]) {
        [self.before_time_label mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(kHorizontalMargin);
            make.bottom.equalTo(self.mas_bottom).offset(-15-50); // 散剂需要额外的50像素空间
            make.height.mas_equalTo(30); // 统一高度为30像素
            make.width.mas_equalTo(80);
        }];

        self.usageBottomLineView.hidden = NO;
    } else {
        [self.before_time_label mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(kHorizontalMargin);
            make.bottom.equalTo(self.mas_bottom).offset(-15); // 距离底部15像素，保持一致
            make.height.mas_equalTo(30); // 统一高度为30像素
            make.width.mas_equalTo(80);
        }];

        self.usageBottomLineView.hidden = YES;
    }
    
    // 构建键名
    NSString *otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EveryDayTimes, drugFormMd5];
    NSString *otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EveryTimesAmount, drugFormMd5];
    NSString *otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@",
        kUsageOtherType_EstimateDays, drugFormMd5];
    
    // 处理快速开方情况
    if (![self.quickOrderTempId isEqualToString:@""]) {
        otherTypeEveryDayTimes = [NSString stringWithFormat:@"%@_%@",
            otherTypeEveryDayTimes, self.quickOrderTempId];
        otherTypeEveryTimesAmount = [NSString stringWithFormat:@"%@_%@",
            otherTypeEveryTimesAmount, self.quickOrderTempId];
        otherTypeEstimateDays = [NSString stringWithFormat:@"%@_%@",
            otherTypeEstimateDays, self.quickOrderTempId];
    }
    
    // 恢复上次修改的内容
    [self restoreOtherTypeValuesWithKeys:@{
        otherTypeEveryDayTimes: _timesTextField,
        otherTypeEveryTimesAmount: _preDoseTextField,
        otherTypeEstimateDays: _dayTextField
    }];
}

- (void)buildAndRestoreKAndYTypeView {
    [self buildYAndKTypeView];
    
    //如果为饮片和代煎
    if ([self.drugForm isEqualToString:@"饮片"] || [self.drugForm isEqualToString:@"代煎"]) {
        [self.before_time_label mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(kHorizontalMargin);
            make.bottom.equalTo(self.mas_bottom).offset(-15-50); // 饮片/代煎需要额外的50像素空间
            make.height.mas_equalTo(30); // 统一高度为30像素
            make.width.mas_equalTo(80);
        }];

        self.usageBottomLineView.hidden = NO;
    } else {
        [self.before_time_label mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(kHorizontalMargin);
            make.bottom.equalTo(self.mas_bottom).offset(-15); // 距离底部15像素，保持一致
            make.height.mas_equalTo(30); // 统一高度为30像素
            make.width.mas_equalTo(80);
        }];

        self.usageBottomLineView.hidden = YES;
    }
    
    // 构建键名
    NSString *kandYTypeAll = @"kUsageKandYType_All";
    NSString *kandYTypeEveryDayAmount = @"kUsageKandYType_EveryDayAmount";
    NSString *kandYTypeTimes = @"kUsageKandYType_times";
    
    // 处理快速开方情况
    if (![self.quickOrderTempId isEqualToString:@""]) {
        kandYTypeAll = [NSString stringWithFormat:@"%@_%@",
            kandYTypeAll, self.quickOrderTempId];
        kandYTypeEveryDayAmount = [NSString stringWithFormat:@"%@_%@",
            kandYTypeEveryDayAmount, self.quickOrderTempId];
        kandYTypeTimes = [NSString stringWithFormat:@"%@_%@",
            kandYTypeTimes, self.quickOrderTempId];
    }
    
    // 恢复上次修改的内容
    [self restoreKAndYTypeValuesWithKeys:@{
        kandYTypeAll: _drugNumTextField,
        kandYTypeEveryDayAmount: _usageTextField,
        kandYTypeTimes: _timesTextField
    }];
}

- (void)restoreOtherTypeValuesWithKeys:(NSDictionary<NSString *, UITextField *> *)keyFieldMap {
    [keyFieldMap enumerateKeysAndObjectsUsingBlock:^(NSString *key, UITextField *textField, BOOL *stop) {
        NSString *value = [Utils getUsageType:key withPatientId:self.patientId];
        if (![value isEqualToString:@"-1"] && ![value isEqualToString:@""]) {
            textField.text = value;
        }
    }];
}

- (void)restoreKAndYTypeValuesWithKeys:(NSDictionary<NSString *, UITextField *> *)keyFieldMap {
    [keyFieldMap enumerateKeysAndObjectsUsingBlock:^(NSString *key, UITextField *textField, BOOL *stop) {
        NSString *value = [Utils getUsageType:key withPatientId:self.patientId];
        if (![value isEqualToString:@"-1"] && ![value isEqualToString:@""]) {
            textField.text = value;
        }
    }];
}

- (void)setTotalDoseStr:(NSString *)totalDoseStr {
    
    _totalDoseStr = totalDoseStr;
    _totalDoseLabel.text = totalDoseStr;
    
    [_totalDoseLabel sizeToFit];
    
    [self layoutIfNeeded];
    
    CGFloat height = _totalDoseLabel.size.height;
    
    [_totalDoseLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
    
    [_bgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(100-20+height);
    }];
    
//    _changeUsageHeight((kTitleViewHeight+50+50+50)-20+(height>20?height:20));
    // 计算基础高度，考虑规格行的影响
    CGFloat baseHeight = _isSpecificationRowVisible ? 130 : 100; // 恢复原来的高度设置
    CGFloat specificationHeight = _isSpecificationRowVisible ? 30 : 0; // 规格行额外高度

    if ([self.drugForm isEqualToString:@"散剂"]) {
        _changeUsageHeight((kTitleViewHeight + baseHeight + 50) - 20 + 20 + 50);
        self.usageBottomLineView.hidden = NO;
    }else{
        // 确保用药时间行有足够的空间
        _changeUsageHeight((kTitleViewHeight + baseHeight + 50) - 20 + 20);
        self.usageBottomLineView.hidden = YES;
    }
}

- (void)buildOtherTypeViewWithDrugForm:(NSString *)drugForm {
    
    [_bgView removeAllSubviews];
    
    UIView *totalDoseLine = [[UIView alloc]init];
    totalDoseLine.frame = CGRectMake(0, 95, SCREEN_WIDTH, .5);
    totalDoseLine.userInteractionEnabled = NO; // 禁用交互，避免拦截输入框的点击事件
    [self addSubview:totalDoseLine];

    UIView *usageLine = [[UIView alloc]init];
    usageLine.frame = CGRectMake(0, 95, SCREEN_WIDTH, .5);
    usageLine.userInteractionEnabled = NO; // 禁用交互，避免拦截输入框的点击事件
    [self addSubview:usageLine];

    [ViewTools drawDashLine:totalDoseLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
    [ViewTools drawDashLine:usageLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];
    
    UILabel *beforeTotalDoseLabel = [[UILabel alloc]init];
    beforeTotalDoseLabel.text = @"预计总重:";
    beforeTotalDoseLabel.textColor = [UIColor br_textBlackColor];
    beforeTotalDoseLabel.font = kFontLight(15);
    [_bgView addSubview:beforeTotalDoseLabel];
    
    _totalDoseLabel = [[UILabel alloc]init];
    _totalDoseLabel.font = kFontLight(14);
//    _totalDoseLabel.adjustsFontSizeToFitWidth = YES;
//    _totalDoseLabel.minimumFontSize = 12;
    _totalDoseLabel.textColor = [UIColor br_textBlackColor];
//    _totalDoseLabel.contentInset = UIEdgeInsetsZero;
//    _totalDoseLabel.textContainerInset = UIEdgeInsetsZero;
//    _totalDoseLabel.editable = NO;
//    _totalDoseLabel.scrollEnabled = NO;
    _totalDoseLabel.numberOfLines = 0;
    _totalDoseLabel.textAlignment = NSTextAlignmentRight;
    [_bgView addSubview:_totalDoseLabel];
    
    [beforeTotalDoseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(kHorizontalMargin);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
        make.top.equalTo(_bgView).offset(15);
    }];
    
    [_totalDoseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      
        make.left.equalTo(beforeTotalDoseLabel.mas_right);
        make.right.equalTo(self).offset(-kHorizontalMargin);
//        make.height.mas_equalTo(20);
        make.centerY.equalTo(beforeTotalDoseLabel);
    }];

#pragma mark 规格行

    // 创建规格行的虚线分隔符
    _specificationLine = [[UIView alloc]init];
    _specificationLine.frame = CGRectMake(0, 95, SCREEN_WIDTH, .5);
    _specificationLine.hidden = !_isSpecificationRowVisible; // 根据可见性设置
    _specificationLine.userInteractionEnabled = NO; // 禁用交互，避免拦截输入框的点击事件
    [self addSubview:_specificationLine];
    [ViewTools drawDashLine:_specificationLine lineLength:5 lineSpacing:4 lineColor:[UIColor br_insideDivisionLineColor]];

    // 创建规格标签
    _specificationLabel = [[UILabel alloc]init];
    _specificationLabel.text = @"规格";
    _specificationLabel.font = kFontLight(16);
    _specificationLabel.textColor = [UIColor br_textBlackColor];
    _specificationLabel.hidden = !_isSpecificationRowVisible; // 根据可见性设置
    [_bgView addSubview:_specificationLabel];

    [_specificationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(kHorizontalMargin);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
        make.top.mas_equalTo(beforeTotalDoseLabel.mas_bottom).offset(30);
    }];

#pragma mark 每日XX次

    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每日";
    before_times_label.font = kFontLight(16);
    before_times_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    
    // 根据剂型设置默认值，这些特殊剂型保持3次，其他剂型为2次
    if ([drugForm isEqualToString:@"胶囊"] || 
        [drugForm isEqualToString:@"散剂"] || 
        [drugForm isEqualToString:@"水丸"] || 
        [drugForm isEqualToString:@"蜜丸"] || 
        [drugForm isEqualToString:@"膏方"]) {
        _timesTextField.text = @"3";
    } else {
        _timesTextField.text = @"2";
    }
    
    _timesTextField.delegate = self;
    _timesTextField.inputView = timeskeyboard;
    [_bgView addSubview:_timesTextField];
    
    // 为 timesTextField 添加点击事件处理，在代煎剂型下跳出选择器
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_times_label];
    
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {

        make.left.equalTo(self).offset(kHorizontalMargin);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
        // 根据规格行是否可见来设置约束
        if (_isSpecificationRowVisible) {
            make.top.mas_equalTo(_specificationLabel.mas_bottom).offset(30);
        } else {
            make.top.mas_equalTo(beforeTotalDoseLabel.mas_bottom).offset(30);
        }

    }];
    
    [_timesTextField mas_remakeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_times_label);
        
    }];
    
    
    __weak __typeof(self)weakSelf = self;
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryDayTimes, drugFormsMd5];
        
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_timesTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {

        make.left.equalTo(self.timesTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_times_label);

    }];

#pragma mark 每次XXg (与每日x次在同一行)

    UILabel *before_preDose_label = [[UILabel alloc]init];
    before_preDose_label.text = @"每次";
    before_preDose_label.font = kFontLight(16);
    before_preDose_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:before_preDose_label];
    
    MMNumberKeyboard *preDosekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    preDosekeyboard.allowsDecimalPoint = NO;
    preDosekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    preDosekeyboard.delegate = self;
    _preDoseTextField = [[BRUnderlineRedTextField alloc]init];
    _preDoseTextField.text = @"30";
    _preDoseTextField.delegate = self;
    _preDoseTextField.inputView = preDosekeyboard;
    [_bgView addSubview:_preDoseTextField];
    
    UILabel *behind_preDose_label = [[UILabel alloc]init];
    behind_preDose_label.text = @"g";
    behind_preDose_label.font = kFontLight(16);
    behind_preDose_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_preDose_label];
    
    //胶囊颗粒数 只有胶囊的时候需要显示
    _capsuleParticleLabel = [[UILabel alloc] init];
    _capsuleParticleLabel.textColor = [UIColor br_textRedColor];
    _capsuleParticleLabel.font = kFontLight(15);
    _capsuleParticleLabel.text = @"(3颗)";
    _capsuleParticleLabel.hidden = YES;
    [_bgView addSubview:_capsuleParticleLabel];
    
    [before_preDose_label mas_makeConstraints:^(MASConstraintMaker *make) {

        if (isiPhone5) {
            make.left.equalTo(behind_times_label.mas_right).offset(5);
        }
        else {
            make.left.equalTo(behind_times_label.mas_right).offset(10);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
        make.centerY.equalTo(before_times_label); // 与"每日x次"在同一行

    }];
    
    [_preDoseTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(before_preDose_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_preDose_label);
        
    }];
    
    [[_preDoseTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EveryTimesAmount, drugFormsMd5];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_preDoseTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_preDose_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self.preDoseTextField.mas_right);
        if (isiPhone5) {
            make.height.and.width.mas_equalTo(15);
        }
        else {
            make.height.and.width.mas_equalTo(20);
        }
        make.centerY.equalTo(before_preDose_label);
        
    }];
    
    
    [_capsuleParticleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(behind_preDose_label);
        make.left.equalTo(behind_preDose_label.mas_right).with.offset(-5);
    }];
    
#pragma mark 预计服用XX天
    
    UILabel *before_day_label = [[UILabel alloc]init];
    before_day_label.text = @"预计服用";
    before_day_label.font = kFontLight(16);
    before_day_label.textAlignment = NSTextAlignmentRight;
    before_day_label.textColor = [UIColor br_textBlackColor];
//    before_day_label.backgroundColor = [UIColor redColor];
    [_bgView addSubview:before_day_label];
    
    _dayTextField = [[BRUnderlineRedTextField alloc]init];
    _dayTextField.font = kFontLight(16);
    _dayTextField.delegate = self;
    _dayTextField.text = @"20";
    _dayTextField.enabled = NO;
    [_bgView addSubview:_dayTextField];
    
    UILabel *behind_day_label = [[UILabel alloc]init];
    behind_day_label.text = @"天";
    behind_day_label.font = kFontLight(16);
    behind_day_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_day_label];
    
    [before_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_dayTextField.mas_left);
        
//        if (isiPhone5) {
////            make.left.equalTo(behind_preDose_label.mas_right);
////            make.width.mas_equalTo(70);
//        }
//        else {
//            make.left.equalTo(behind_preDose_label.mas_right).offset(25);
////            make.width.mas_equalTo(80);
//        }
        make.height.mas_equalTo(30); // 统一高度为30像素，与其他行保持一致
        make.centerY.equalTo(before_preDose_label);
        
    }];
    
    [_dayTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        
//        make.left.equalTo(before_day_label.mas_right);
        make.right.equalTo(behind_day_label.mas_left);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_day_label);
        
    }];
    
    UIImage *questionImage = [UIImage imageNamed:@"yangshengpu_jiangli"];
    UIButton *questionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [questionButton setImage:questionImage forState:UIControlStateNormal];
    [_bgView addSubview:questionButton];
    
    [questionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(questionImage.size);
        make.centerY.equalTo(before_day_label);
        make.right.equalTo(before_day_label.mas_left);
    }];
    
    [questionButton addTarget:self action:@selector(clickOtehrTypeMarkButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    
    [[_dayTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *drugFormsMd5 = [[weakSelf.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@_%@",kUsageOtherType_EstimateDays, drugFormsMd5];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0) {
            type = [NSString stringWithFormat:@"%@_%@",type,weakSelf.quickOrderTempId];
        }
        
        NSString *text = [_dayTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_day_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
//        make.left.equalTo(self.dayTextField.mas_right);
        make.right.mas_equalTo(@(-kHorizontalMargin));
        make.height.mas_equalTo(20);
//        make.width.mas_equalTo(20);
        make.centerY.equalTo(before_preDose_label);
        
    }];
    
    // 特殊剂型的默认值单独设置
    if ([drugForm isEqualToString:@"蜜丸"] || [drugForm isEqualToString:@"水丸"]) {
        
        _timesTextField.text = @"3";  // 蜜丸、水丸默认为每日3次
        _preDoseTextField.text = @"6";
        
        [_preDoseTextField addTarget:self action:@selector(textFieldEditingDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
        
    }
    else if ([drugForm isEqualToString:@"胶囊"]){
       
        _timesTextField.text = @"3";  // 胶囊默认为每日3次
        _preDoseTextField.text = @"3";
        
        [_preDoseTextField addTarget:self action:@selector(capsuleTextFieldEditingDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
    }
    else if ([drugForm isEqualToString:@"散剂"]) {
        
        _timesTextField.text = @"3";  // 散剂默认为每日3次
        _preDoseTextField.text = @"10";
        
    }
    else if ([drugForm isEqualToString:@"膏方"]) {
        
        _timesTextField.text = @"3";  // 膏方默认为每日3次
        _preDoseTextField.text = @"30";
        
    }
    
    [totalDoseLine mas_makeConstraints:^(MASConstraintMaker *make) {

        make.top.equalTo(beforeTotalDoseLabel.mas_bottom).offset(15);
        make.left.and.right.equalTo(self);
        make.height.mas_equalTo(.5);

    }];

    // 为规格行虚线添加约束
    [_specificationLine mas_makeConstraints:^(MASConstraintMaker *make) {

        make.top.equalTo(_specificationLabel.mas_bottom).offset(15);
        make.left.and.right.equalTo(self);
        make.height.mas_equalTo(.5);

    }];

    [usageLine mas_makeConstraints:^(MASConstraintMaker *make) {

        make.top.equalTo(before_preDose_label.mas_bottom).offset(15); // 改为相对于"每次xg"行定位
        make.left.and.right.equalTo(self);
        make.height.mas_equalTo(.5);

    }];
    
    [_bgView mas_updateConstraints:^(MASConstraintMaker *make) {
        // 根据规格行是否可见来调整高度
        if (_isSpecificationRowVisible) {
            make.height.mas_equalTo(130); // 增加30像素高度以适应规格行
        } else {
            make.height.mas_equalTo(100); // 恢复原来的高度
        }
    }];

    // 通知父视图更新整体高度
    if (self.changeUsageHeight) {
        CGFloat newHeight = _isSpecificationRowVisible ? (kTitleViewHeight + 130 + 50) : (kTitleViewHeight + 100 + 50);
        self.changeUsageHeight(newHeight);
    }

}

- (void)buildYAndKTypeView {
    
    [_bgView removeAllSubviews];
    
    [_bgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
    }];
    
    [_drugNumTextField addTarget:self action:@selector(textFieldEditChanged:) forControlEvents:UIControlEventEditingChanged];
    
#pragma mark 共XX剂
    
    UILabel *before_drugNum_label = [[UILabel alloc]init];
    before_drugNum_label.text = @"共";
    before_drugNum_label.font = kFontLight(16);
    before_drugNum_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:before_drugNum_label];
    
    MMNumberKeyboard *drugNumkeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    drugNumkeyboard.allowsDecimalPoint = NO;
    drugNumkeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    drugNumkeyboard.delegate = self;
    _drugNumTextField = [[BRUnderlineRedTextField alloc]init];
    _drugNumTextField.text = @"7";
    _drugNumTextField.delegate = self;
    _drugNumTextField.inputView = drugNumkeyboard;
    [_bgView addSubview:_drugNumTextField];
    
    UILabel *behind_drugNum_label = [[UILabel alloc]init];
    behind_drugNum_label.text = @"剂";
    behind_drugNum_label.font = kFontLight(16);
    behind_drugNum_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_drugNum_label];
    
    [before_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self).offset(kHorizontalMargin);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(self.bgView);
        
    }];
    
    [_drugNumTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(before_drugNum_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_drugNum_label);
        
    }];
    
    __weak __typeof(self)weakSelf = self;
    [[_drugNumTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        NSString *text = [_drugNumTextField text];
//        NSLog(@"drug num text == %@",text);
//        NSString *drugFormMd5 = [[self.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_All];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_drugNum_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self.drugNumTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_drugNum_label);
        
    }];
    
#pragma mark 用量
    
    UILabel *before_usage_label = [[UILabel alloc]init];
    before_usage_label.text = @"每日";
    before_usage_label.font = kFontLight(16);
    before_usage_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:before_usage_label];
    
    MMNumberKeyboard *usagekeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    usagekeyboard.allowsDecimalPoint = NO;
    usagekeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    usagekeyboard.delegate = self;
    _usageTextField = [[BRUnderlineRedTextField alloc]init];
    _usageTextField.text = @"1";
    _usageTextField.delegate = self;
    _usageTextField.inputView = usagekeyboard;
    [_bgView addSubview:_usageTextField];
    
    UILabel *behind_usage_label = [[UILabel alloc]init];
    behind_usage_label.text = @"剂";
    behind_usage_label.font = kFontLight(16);
    behind_usage_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_usage_label];
    
    [before_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        if (isiPhone5) {
            make.left.equalTo(behind_drugNum_label.mas_right).offset(5);
        }
        else {
            make.left.equalTo(behind_drugNum_label.mas_right).offset(10);
        }
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(self.bgView);
        
    }];
    
    [_usageTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(before_usage_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_usage_label);
        
    }];
    
    [[_usageTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
//        NSString *drugFormMd5 = [[self.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_EveryDayAmount];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_usageTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_usage_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self.usageTextField.mas_right);
        make.height.and.width.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
        
    }];
    
#pragma mark 分几次服用
    
    UILabel *before_times_label = [[UILabel alloc]init];
    before_times_label.text = @"每剂分";
    before_times_label.font = kFontLight(16);
    before_times_label.textAlignment = NSTextAlignmentRight;
    before_times_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:before_times_label];
    
    MMNumberKeyboard *timeskeyboard = [[MMNumberKeyboard alloc] initWithFrame:CGRectZero];
    timeskeyboard.allowsDecimalPoint = NO;
    timeskeyboard.preferredStyle = MMNumberKeyboardStylePlainButtons;
    timeskeyboard.delegate = self;
    _timesTextField = [[BRUnderlineRedTextField alloc]init];
    _timesTextField.font = kFontLight(16);
    _timesTextField.delegate = self;
    _timesTextField.text = @"2";  // 颗粒、饮片、代煎和外用中药等普通剂型默认为2次
    _timesTextField.inputView = timeskeyboard;
    [_bgView addSubview:_timesTextField];
    
    // 为 timesTextField 添加点击事件处理，在代煎剂型下跳出选择器
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchDown];
    [_timesTextField addTarget:self action:@selector(timesTextFieldTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    UILabel *behind_times_label = [[UILabel alloc]init];
    behind_times_label.text = @"次服用";
    behind_times_label.font = kFontLight(16);
    behind_times_label.textColor = [UIColor br_textBlackColor];
    [_bgView addSubview:behind_times_label];
    
    [before_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        if (isiPhone5) {
            make.left.equalTo(behind_usage_label.mas_right).offset(5);
            make.width.mas_equalTo(50);
        }
        else {
            make.left.equalTo(behind_usage_label.mas_right).offset(10);
            make.width.mas_equalTo(60);
        }
        make.height.mas_equalTo(20);
        make.centerY.equalTo(before_usage_label);
        
    }];
    
    [_timesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(before_times_label.mas_right);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(30);
        make.centerY.equalTo(before_usage_label);
        
    }];
    
    [[_timesTextField rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
//        NSString *drugFormMd5 = [[self.drugForm md5String] substringToIndex:5];
        NSString *type = [NSString stringWithFormat:@"%@",kUsageKandYType_times];
        //如果为快速开方
        if(weakSelf.quickOrderTempId.length > 0){
            type = [NSString stringWithFormat:@"%@_%@",type, weakSelf.quickOrderTempId];
        }
        NSString *text = [_timesTextField text];
        [Utils saveUsageType:type text:text withPatientId:self.patientId];
    }];
    
    [behind_times_label mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self.timesTextField.mas_right);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(60);
        make.centerY.equalTo(before_usage_label);
        
    }];
    
}

- (void)textFieldEditChanged:(NSNotification *)obj {
    
    UITextField *textField = (UITextField *)obj.object;
    NSString *toBeString = textField.text;
    
    if (_drugChanged) {
        _drugChanged(toBeString);
    }
    
}

- (void)textFieldEditingDidBegin:(NSNotification *)obj {
    
    if (_textFieldEditingDidBegin) {
        _textFieldEditingDidBegin();
    }
        
}

- (void)capsuleTextFieldEditingDidBegin:(NSNotification *)obj {
    
    if (_capsuleInputEditingDidbegin) {
        _capsuleInputEditingDidbegin();
    }
}

- (BOOL)numberKeyboard:(MMNumberKeyboard *)numberKeyboard shouldInsertText:(NSString *)text {
    
    if ([_dayTextField isFirstResponder]) {
        return NO;
    }
    
    return YES;
    
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    
    if (textField == _drugNumTextField) {
        
        NSString *drugNum = [_drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        if (drugNum.length == 0 || [drugNum integerValue] == 0) {
            
        }
        else {
        
            NSString *usageNum = [_usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            
            _drugChanged(drugNum);
            
            if (usageNum.length == 0 || [usageNum integerValue] == 0) {
                if (_changeDay) {
                    _changeDay(drugNum);
                }
            }
            else {
                int times = [drugNum intValue]/[usageNum intValue];
                if (_changeDay) {
                    _changeDay([NSString stringWithFormat:@"%d",times == 0? 1:times]);
                }
            }
            
        }
        
    }
    
    if (textField  == _usageTextField) {
        
        NSString *usageNum = [_usageTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (usageNum.length == 0 || [usageNum integerValue] == 0) {
            
        }
        else {
            
            NSString *drugNum = [_drugNumTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
            if (drugNum.length == 0 || [usageNum integerValue] ==0) {
                
            }
            else {
                int times = [drugNum intValue]/[usageNum intValue];
                if (_changeDay) {
                    _changeDay([NSString stringWithFormat:@"%d",times == 0? 1:times]);
                }
            }
            
        }
        
    }
    
    if ((textField == _timesTextField || textField == _preDoseTextField) &&
        ![_drugForm isEqualToString:@"颗粒"] &&
        ![_drugForm isEqualToString:@"饮片"] &&
        ![_drugForm isEqualToString:@"代煎"] &&
        ![_drugForm isEqualToString:@"外用中药"]) {
        
        if (_totalDoseLabel.text.length == 0) {
            
        }
        else {
            
            NSString *doseStr = [[_totalDoseLabel.text componentsSeparatedByString:@"g"] firstObject];
            
            if ([doseStr floatValue] == 0) {
                
            }
            else {
                
                NSString *timesNum = [_timesTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                NSString *preDoseNum = [_preDoseTextField.text stringByReplacingOccurrencesOfString:@" " withString:@""];
                if ([timesNum intValue] == 0 || [preDoseNum integerValue] == 0 || timesNum.length == 0 || preDoseNum.length == 0) {
                    
                }
                else {
                    
                    CGFloat totalDose = [doseStr integerValue];
                    NSInteger day = totalDose/([timesNum integerValue]*[preDoseNum floatValue]);
                    if (_changeDay) {
                        _changeDay([NSString stringWithFormat:@"%ld",day == 0? 1:day]);
                    }
                    
                }
                
            }
            
        }
        
    }
    
}

#pragma mark - click event
- (void)clickOtehrTypeMarkButtonEvent:(UIButton *)sender {
    if (self.clickOtherTypeBlock) {
        self.clickOtherTypeBlock();
    }
}

// 添加timesTextField的点击处理方法
- (void)timesTextFieldTapped:(UITextField *)sender {
    // 判断当前剂型是否为代煎
    if ([_drugForm isEqualToString:@"代煎"]) {
        // 如果是代煎剂型，执行回调打开次数选择器
        if (self.servingTimesSelectionBlock) {
            NSLog(@"触发代煎次数选择器");
            self.servingTimesSelectionBlock();
            // 阻止默认的键盘弹出
            [sender resignFirstResponder];
        }
    }
}

// 在textFieldShouldBeginEditing方法中添加对代煎剂型"每剂分几次服用"输入框的特殊处理
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField {
    // 如果是timesTextField（每剂分几次服用）且剂型为代煎，则不允许键盘弹出
    if (textField == _timesTextField && [_drugForm isEqualToString:@"代煎"]) {
        NSLog(@"阻止代煎剂型下的timesTextField显示键盘");
        // 触发选择器
        if (self.servingTimesSelectionBlock) {
            self.servingTimesSelectionBlock();
        }
        return NO;
    }
    
    return YES;
}

#pragma mark - 规格行控制方法

// 设置规格行的显示状态
- (void)setSpecificationRowVisible:(BOOL)visible {
    _isSpecificationRowVisible = visible;

    // 如果规格行还没有创建，只设置状态，等待后续创建时使用
    if (!_specificationLabel) {
        return;
    }

    if (_specificationLabel) {
        _specificationLabel.hidden = !visible;

        // 同时控制规格行虚线的显示
        if (_specificationLine) {
            _specificationLine.hidden = !visible;
        }

        // 更新视图高度
        [_bgView mas_updateConstraints:^(MASConstraintMaker *make) {
            if (visible) {
                make.height.mas_equalTo(130); // 增加30像素高度以适应规格行
            } else {
                make.height.mas_equalTo(100);
            }
        }];

        // 重新布局"每日XX次"行的约束
        UILabel *before_times_label = nil;
        UILabel *beforeTotalDoseLabel = nil;

        // 找到相关标签
        for (UIView *subview in _bgView.subviews) {
            if ([subview isKindOfClass:[UILabel class]]) {
                UILabel *label = (UILabel *)subview;
                if ([label.text isEqualToString:@"每日"]) {
                    before_times_label = label;
                } else if ([label.text isEqualToString:@"预计总重:"]) {
                    beforeTotalDoseLabel = label;
                }
            }
        }

        if (before_times_label && beforeTotalDoseLabel) {
            [before_times_label mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self).offset(kHorizontalMargin);
                make.width.mas_equalTo(40);
                make.height.mas_equalTo(20);

                if (visible && _specificationLabel) {
                    make.top.mas_equalTo(_specificationLabel.mas_bottom).offset(30);
                } else {
                    make.top.mas_equalTo(beforeTotalDoseLabel.mas_bottom).offset(30);
                }
            }];
        }

        // 通知父视图更新高度
        if (self.changeUsageHeight) {
            CGFloat newHeight = visible ? (kTitleViewHeight + 130 + 50) : (kTitleViewHeight + 100 + 50);
            self.changeUsageHeight(newHeight);
        }
    }
}

// 获取规格行的显示状态
- (BOOL)isSpecificationRowVisible {
    return _isSpecificationRowVisible;
}

@end
