# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

always response in 中文。

## 项目概述

必然中医 (BRZY) 是一个中医远程医疗 iOS 应用，使用 Objective-C 开发。主要功能包括医患聊天、处方管理、支付系统等。

对应的还有相同功能的安卓项目，安卓项目在目录 /Volumes/MacExtra/必然中医/brzy_android ，你有权限使用 filesystem 的 mcp server 进行查看。 iOS 和安卓端的功能和 UI 界面是高度一致的，如果没有特殊情况和说明，两种的功能和 UI 等应该是尽可能一致的。 

## 开发环境设置

### 依赖管理
```bash
# 安装 CocoaPods 依赖
pod install

# 必须使用 workspace 打开项目
open BRZY.xcworkspace
```

### 构建命令
```bash
# 清理构建
xcodebuild clean -workspace BRZY.xcworkspace -scheme BRZY

# 模拟器构建
xcodebuild build -workspace BRZY.xcworkspace -scheme BRZY -destination 'platform=iOS Simulator,name=iPhone 14'

# 真机构建
xcodebuild build -workspace BRZY.xcworkspace -scheme BRZY -destination 'generic/platform=iOS'
```

## 项目架构

### 目录结构
```
BRZY/
├── AppDelegate.m           # 应用入口，模块管理器初始化
├── Classes/
│   ├── General/           # 基础类、分类、工具类
│   ├── Helpers/           # 网络请求、文件管理等辅助类
│   ├── Macro/             # 常量定义、宏定义
│   └── Sections/          # 功能模块（各个界面的 ViewController）
└── Assets.xcassets/       # 图片资源
```

### 核心架构组件

- **模块管理**: `BRModuleManager` - 基于 plist 配置的模块化启动系统
- **用户管理**: `UserManager` - 单例模式的用户状态管理
- **即时通讯**: `IMClient` - 自定义 Socket 通信实现的聊天系统
- **数据存储**: WCDB - 本地数据库，存储聊天记录等数据
- **网络请求**: AFNetworking 3.0.4 + 自定义封装

### 关键技术点

1. **Socket 通信**: 使用 CocoaAsyncSocket 实现 TCP 长连接，支持 SSL
2. **推送服务**: 集成极光推送（JPush）无 IDFA 版本
3. **支付集成**: 微信支付、银行卡、提现功能
4. **OCR 识别**: 腾讯云 OCR 用于处方扫描
5. **微信集成**: 登录、分享功能（AppID: wx266e4342efb1a84b）

## 开发注意事项

### API 环境
- 生产环境: `https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController`
- Socket 服务器: `im.haoniuzhongyi.top:5333` (SSL)

### 代码规范
- 使用 Objective-C，遵循苹果命名规范
- 类名前缀使用 `BR`
- 使用 Masonry 进行自动布局
- 网络请求统一使用项目封装的网络层

### 功能模块
主要功能模块位于 `Classes/Sections/`:
- `我的钱包` - 支付、提现相关
- `CommonlyPrescription` - 常用处方
- `PatientDocument` - 患者档案
- `BRIntelligent` - 智能功能
- `VisitsArrangement` - 出诊安排

### 依赖库版本
关键依赖库版本已在 Podfile 中锁定，修改版本需谨慎测试：
- iOS 最低支持版本: 13.0
- 使用 `use_frameworks!` 动态库方式
- 特殊配置: WCDB 需要 C++14 标准

### 调试技巧
1. IM 连接问题查看 `IMClient` 日志
2. 网络请求失败检查 `Helpers` 中的网络封装类
3. 推送问题查看 JPush 相关配置
4. 支付问题检查微信 SDK 配置和 Universal Links


